<?xml version="1.0" encoding="UTF-8"?>
<svg width="378px" height="61px" preserveAspectRatio="none meet" viewBox="0 0 378 61" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组-3备份-3</title>
    <defs>
        <linearGradient x1="64.6452522%" y1="23.0577428%" x2="39.6146786%" y2="59.1469816%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="36%"></stop>
            <stop stop-color="#000000" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="56.840514%" y1="53.9996723%" x2="45.8805745%" y2="44.5355809%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="49%"></stop>
            <stop stop-color="#000000" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="通报模版" transform="translate(-144, -142)" fill-rule="nonzero">
            <g id="编组-3备份-3" transform="translate(144, 142)">
                <text id="r" fill="#FFFFFF" font-family="MicrosoftYaHei, Microsoft YaHei" font-size="9.12" font-weight="bold">
                    <tspan x="130.745917" y="40.1917293">r</tspan>
                </text>
                <polygon id="路径" fill="#EC4948" points="154.241379 52.2857143 116.640677 0 0 0 0 19.3223847"></polygon>
                <polygon id="路径" fill="#DC4A48" points="152.750401 52.2857143 282.413793 0 115.137931 0"></polygon>
                <polygon id="路径" fill="#DC4A48" points="378 61 378 0 281.158872 0 215.068966 26.303"></polygon>
                <polygon id="路径" fill="url(#linearGradient-1)" opacity="0.1" style="mix-blend-mode: multiply;" points="152.341075 52.2857143 167.275862 46.1994377 141.805456 0 115.137931 0"></polygon>
                <polygon id="路径" fill="url(#linearGradient-2)" opacity="0.4" style="mix-blend-mode: multiply;" points="217.241379 25.7576497 261.137932 34.8571429 308.482759 0 283.738915 0"></polygon>
            </g>
        </g>
    </g>
</svg>