<template>
  <div class="timeline-content">
    <div class="timeline-content-title">
      <div class="instruction-status">
        <img src="@/assets/images/simulatedVS/yellowStar.png" alt="">
        {{commentType=='8'?scoreStageName:stageName }}
      </div>
      <div style="display: flex; align-items: center;">
        <div v-if="commentType=='2' && relatedCommentType" class="related-content-box">
          {{ relatedCommentType == 4 ? '情况通报' : '分析报告' }}
        </div>
        <div v-show="showInstruction && commentOrder" class="instruction-box">
          {{`指令${commentOrder||''}`}}
        </div>
        <div class="amplifyBtn" @click.stop="$emit('amplify', $event)"></div>
      </div>
    </div>
    <div class="timeline-body">
      <slot></slot>
      <!-- <div class="creater-box">
        {{ item.role==1?'队长':'队员' }}：
        {{ item.nickName||'' }}
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimelineContentTemplate',
  props: {
    // 主要数据对象，包含所有时间线内容相关信息
    item: {
      type: Object,
      default: () => ({})
    },
    // 配置类属性，用于控制组件行为
    showInstruction: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 从 item 对象获取评论类型，提供默认值和空值处理
    commentType() {
      return this.item?.commentType || '';
    },

    // 从 item 对象获取评分阶段名称，提供默认值和空值处理
    scoreStageName() {
      return this.item?.scoreStageName || '';
    },

    // 从 item 对象获取阶段名称，提供默认值和空值处理
    stageName() {
      return this.item?.stageName || '';
    },

    // 从 item 对象获取相关评论类型，提供默认值和空值处理
    relatedCommentType() {
      return this.item?.relatedCommentType || '';
    },

    // 从 item 对象获取评论顺序，提供默认值和空值处理
    commentOrder() {
      return this.item?.commentOrder || '';
    },

    // 从 item 对象获取团队类型，提供默认值和空值处理
    teamType() {
      return this.item?.teamType || 2; // 默认蓝方
    }
  }
}
</script>

<style scoped lang="scss">
.timeline-content {
  background: #02004D;
  box-shadow: inset 0 0 20em 0 rgba(17, 40, 255, 0.66);
  border: 2em solid #0543C6;

  .timeline-content-title {
    font-size: 13em;
    padding: 0.4em 0.5em;
    border-bottom: 0.01em solid #709EFF;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .instruction-status {
      display: flex;
      align-items: center;

      img {
        height: 1.3em;
        margin-right: 0.5em;
      }
    }

    .instruction-box {
      background-image: url("../../../assets/images/simulatedVS/instructionBlue.png");
      padding: 0.3em 0.5em 0.3em 2em;
      background-repeat: no-repeat;
      background-position: bottom;
      background-size: 100% 100%;
    }

    .amplifyBtn {
      cursor: pointer;
      width: 1.5em;
      height: 1.5em;
      margin-left: 0.5em;
      background-image: url("../../../assets/images/simulatedVS/amplifyIcon.svg");
      background-repeat: no-repeat;
      background-position: bottom;
      background-size: 100% 100%;

      &:hover {
        background-image: url("../../../assets/images/simulatedVS/amplifyIcon-hover.svg");
      }
    }

    .related-content-box {
      // padding: 0.3em 0.5em 0.3em 1.5em;
      margin-right: 0.5em;
      // background-image: url("../../../assets/images/simulatedVS/instructionBlue.png");
      // background-repeat: no-repeat;
      // background-position: bottom;
      // background-size: 100% 100%;
    }
  }

  .timeline-body {
    padding: 8em;

    .creater-box {
      font-size: 8em;
      margin-top: 1em;
      text-align: end;
    }
  }

  // 红方样式
  &.red-team {
    background: #4A0303;
    box-shadow: inset 0 0 20em 0 rgba(212, 21, 3, 0.66);
    border: 2em solid #D41503;

    .timeline-content-title {
      border-bottom: 0.01em solid #FF948A;

      .instruction-box {
        background-image: url("../../../assets/images/simulatedVS/instructionRed.png");
      }

      .related-content-box {
        background-image: url("../../../assets/images/simulatedVS/instructionRed.png");
      }
    }
  }
}
</style>
