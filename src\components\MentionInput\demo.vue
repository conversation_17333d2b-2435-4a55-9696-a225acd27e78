<template>
  <div class="mention-demo">
    <h2>MentionInput 组件演示</h2>
    
    <div class="demo-section">
      <h3>基本用法</h3>
      <p>在输入框中输入 @ 符号来艾特用户，艾特的用户会显示为蓝色标签，可以整体删除。</p>
      
      <mention-input
        v-model="content1"
        type="textarea"
        placeholder="请输入内容，使用 @ 可以艾特其他用户"
        :autosize="{ minRows: 4, maxRows: 6 }"
        style="width: 100%; margin-bottom: 20px;"
      />
      
      <div class="result">
        <strong>输入内容：</strong>
        <pre>{{ content1 }}</pre>
      </div>
    </div>

    <div class="demo-section">
      <h3>带任务ID的用法</h3>
      <p>指定 drillTaskId 后，只会显示参与该任务的用户。</p>
      
      <mention-input
        v-model="content2"
        type="textarea"
        :drill-task-id="drillTaskId"
        placeholder="请输入内容，使用 @ 可以艾特任务参与者"
        :autosize="{ minRows: 4, maxRows: 6 }"
        style="width: 100%; margin-bottom: 20px;"
        ref="mentionInput2"
      />
      
      <div class="result">
        <strong>输入内容：</strong>
        <pre>{{ content2 }}</pre>
        
        <strong>艾特的用户：</strong>
        <pre>{{ JSON.stringify(mentionedUsers, null, 2) }}</pre>
      </div>
    </div>

    <div class="demo-section">
      <h3>功能测试</h3>
      <el-button @click="getMentionedUsers">获取艾特用户</el-button>
      <el-button @click="clearContent">清空内容</el-button>
      <el-button @click="insertSampleMention">插入示例艾特</el-button>
    </div>

    <div class="demo-section">
      <h3>使用说明</h3>
      <ul>
        <li>输入 @ 符号触发用户选择下拉框</li>
        <li>可以通过用户名或昵称搜索用户</li>
        <li>选择用户后会插入蓝色的艾特标签</li>
        <li>使用 Backspace 或 Delete 键可以整体删除艾特标签</li>
        <li>光标不会停留在艾特标签内部</li>
        <li>支持复制粘贴，艾特标签会保持格式</li>
      </ul>
    </div>
  </div>
</template>

<script>
import MentionInput from './index.vue'

export default {
  name: 'MentionDemo',
  components: {
    MentionInput
  },
  data() {
    return {
      content1: '',
      content2: '',
      drillTaskId: '12345',
      mentionedUsers: []
    }
  },
  methods: {
    getMentionedUsers() {
      if (this.$refs.mentionInput2) {
        this.mentionedUsers = this.$refs.mentionInput2.getMentionedUsers()
        this.$message.success('已获取艾特用户列表，请查看下方显示')
      }
    },
    
    clearContent() {
      this.content1 = ''
      this.content2 = ''
      this.mentionedUsers = []
      this.$message.info('内容已清空')
    },
    
    insertSampleMention() {
      // 这里可以插入一个示例艾特用户
      this.content1 = '大家好！@张三 @李四 请查看这个重要通知。'
      this.$message.info('已插入示例艾特内容')
    }
  }
}
</script>

<style scoped>
.mention-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.demo-section h3 {
  margin-top: 0;
  color: #303133;
}

.demo-section p {
  color: #606266;
  margin-bottom: 15px;
}

.result {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.result strong {
  color: #409eff;
}

.result pre {
  margin: 5px 0;
  padding: 8px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.demo-section ul {
  color: #606266;
  padding-left: 20px;
}

.demo-section li {
  margin-bottom: 8px;
}
</style>
