<?xml version="1.0" encoding="UTF-8"?>
<svg width="1080px" height="74px" preserveAspectRatio="none meet" viewBox="0 0 1080 74" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 3</title>
    <defs>
        <linearGradient x1="-3.93782229e-14%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#06E9F1" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#0543C6" offset="30.7159113%"></stop>
            <stop stop-color="#D41503" offset="71.2412587%"></stop>
            <stop stop-color="#D41503" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-3.93782229e-14%" y1="50%" x2="100%" y2="50%" id="linearGradient-2">
            <stop stop-color="#06E9F1" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#0543C6" offset="30.7159113%"></stop>
            <stop stop-color="#D41503" offset="71.2412587%"></stop>
            <stop stop-color="#D41503" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="投屏1" transform="translate(-374, -162)">
            <g id="编组-3" transform="translate(374, 162)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="1080" height="73.5"></rect>
                <rect id="矩形" fill="url(#linearGradient-2)" transform="translate(540, 0.75) scale(1, -1) translate(-540, -0.75)" x="0" y="0" width="1080" height="1.5"></rect>
                <rect id="矩形" fill="url(#linearGradient-2)" transform="translate(540, 72.75) scale(1, -1) translate(-540, -72.75)" x="0" y="72" width="1080" height="1.5"></rect>
            </g>
        </g>
    </g>
</svg>