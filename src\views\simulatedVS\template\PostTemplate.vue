<template>
  <div class="template-bltw" :commentId="item.commentId">
    <div class="post-block">
      <img :src="getAvatar(avatar)" alt="用户头像">
      <div class="main-content">
        <div>
          <div class="user-name">
            <span class="text">{{ nickName }}{{ groupName ? '(' + groupName + ')' : '' }}</span>
            <img src="@/assets/images/simulatedVS/vip7.png" alt="">
            <span style="flex: 1;"></span>

            <!-- AI对抗按钮 -->
            <span v-hasRole="['moderator']">
              <span v-if="AIFighting" class="AI-btn fighting" @click="handleStopAI">
                <img class="AI-icon" src="@/assets/images/simulatedVS/AI-icon.png" alt="">对抗
                <img class="play-icon" src="@/assets/images/simulatedVS/stopPlay.svg" alt=""></img>
              </span>
              <span v-else class="AI-btn" @click="handleStartAI">
                <img class="AI-icon" src="@/assets/images/simulatedVS/AI-icon.png" alt="">对抗
                <img class="play-icon" src="@/assets/images/simulatedVS/startPlay.svg" alt=""></img>
              </span>
            </span>

            <img v-if="teamType==2" src="@/assets/images/simulatedVS/teamPointBlue.png" alt="">
            <img v-if="teamType==1" src="@/assets/images/simulatedVS/teamPointRed.png" alt="">
          </div>
          <div class="createTime">{{createTime}}</div>
          <div class="content">{{ content }}</div>
        </div>
        <template v-if="fileList && fileList.length>0">
          <div class="file-list" v-for="itemInside in fileList" :key="itemInside.key"
            :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
            <!-- 图片类型 -->
            <template v-if="isImageType(itemInside.type)">
              <el-image class="img-preview" style="width: 100px; height: 100px" :src="buildFileUrl(itemInside.url)"
                :preview-src-list="[buildFileUrl(itemInside.url)]">
              </el-image>
            </template>

            <!-- 视频类型 -->
            <template v-else-if="isVideoType(itemInside.type)">
              <video style="width: 100%; height: auto;max-height: 20em;" :controls="videoControls">
                <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                您的浏览器不支持 video 标签
              </video>
            </template>

            <!-- 其他类型 -->
            <template v-else>
              <div class="file-preview">
                <i class="el-icon-document"></i>
                <span>{{ itemInside.name }}</span>
              </div>
            </template>
          </div>
        </template>
        <div class="bottom-icon">
          <div class="icon-item">
            <img src="@/assets/images/simulatedVS/shareIcon.png" alt="">
            <span class="text" style="filter: blur(0.15em);">99999</span>
          </div>
          <div class="icon-item">
            <img src="@/assets/images/simulatedVS/commentIcon.png" alt="" @click="$emit('comment', item)">
            <span class="text">{{ actualCommentCount }}</span>
          </div>
          <div class="icon-item">
            <img
              :src="require(isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
              @click="$emit('like', item)">
            <span class="text">{{ likeCount }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showCommentBlock" class="comment-block-top">
      <div class="top-left">评论&nbsp;{{ actualCommentCount }}</div>
      <div class="sort-buttons">
        <button :class="['sort-btn', { active: currentSortBy === 'createdTime' }]" @click="changeSortBy('createdTime')">
          按时间
        </button>
        <button :class="['sort-btn', { active: currentSortBy === 'likeCount' }]" @click="changeSortBy('likeCount')">
          按点赞
        </button>
      </div>
    </div>
    <!-- 评论区 -->
    <div v-loading="commentLoading">
      <div v-if="showCommentBlock" class="comment-block" ref="commentBlockRef">
        <!-- 加载状态 -->
        <!-- <div v-if="commentLoading" class="loading-container">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div> -->

        <!-- v-for 循环评论 -->
        <div class="comment-item" v-for="(replyItem,replyIndex) in displayComments"
          :commentReplyId="replyItem.commentReplyId" :key="`comment-${replyItem.commentReplyId}-${currentSortBy}`">
          <div class="top-comment">
            <img class="avatarImg" :src="getAvatar(replyItem.avatar)" alt="用户头像">
            <div class="main-content">
              <div>
                <div class="user-name">
                  {{ replyItem.nickName }}{{ replyItem.groupName ? '(' + replyItem.groupName + ')': ''}}
                </div>
                <div class="content">:&nbsp;<span v-html="markMention(replyItem.content,replyItem.mentions)"></span>
                </div>
              </div>

              <template v-if="replyItem.fileList && replyItem.fileList.length>0">
                <div class="file-list" v-for="itemInside in replyItem.fileList" :key="itemInside.key"
                  :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
                  <!-- 图片类型 -->
                  <template v-if="isImageType(itemInside.type)">
                    <el-image class="img-preview" style="width: 100px; height: 100px"
                      :src="buildFileUrl(itemInside.url)" :preview-src-list="[buildFileUrl(itemInside.url)]">
                    </el-image>
                  </template>

                  <!-- 视频类型 -->
                  <template v-else-if="isVideoType(itemInside.type)">
                    <video style="width: 100%; height: auto;max-height: 20em;" :controls="videoControls">
                      <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                      您的浏览器不支持 video 标签
                    </video>
                  </template>

                  <!-- 其他类型 -->
                  <template v-else>
                    <div class="file-preview">
                      <i class="el-icon-document"></i>
                      <span>{{ itemInside.name }}</span>
                    </div>
                  </template>
                </div>
              </template>
              <div class="comment-bottom">
                <div class="createTime">{{replyItem.createdTime}}</div>
                <div class="option-icon">
                  <img src="@/assets/images/simulatedVS/commentIcon.png" alt="" style="margin-right: 1.5em;"
                    @click="$emit('comment', replyItem)">
                  <img
                    :src="require(replyItem.isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
                    alt="" @click="$emit('like', replyItem)">
                  <span class="text">{{ replyItem.likeCount||0 }}</span>
                </div>
              </div>
            </div>

            <img class="team-point" v-if="replyItem.teamType==2" src="@/assets/images/simulatedVS/teamPointBlue.png"
              alt="">
            <img class="team-point" v-else-if="replyItem.teamType==1" src="@/assets/images/simulatedVS/teamPointRed.png"
              alt="">
            <div class="team-point" v-else></div>
          </div>

          <!-- 回复评论的评论 -->
          <template v-if="replyItem.children && replyItem.children.length>0">
            <!-- 回复列表容器 -->
            <div v-show="!replyItem.isRepliesCollapsed" class="replies-container">
              <div class="commentInComment" v-for="replySecond in replyItem.children"
                :commentReplyId="replySecond.commentReplyId"
                :key="`commentSecond-${replySecond.commentReplyId}-${currentSortBy}`">
                <img class="avatarImg" :src="getAvatar(replySecond.avatar)" alt="用户头像">
                <div class="main-content">
                  <div>
                    <div class="user-name">
                      {{ replySecond.nickName }}{{ replySecond.groupName ? '(' + replySecond.groupName + ')': ''}}
                      {{ replySecond.replyType=='3'
                      ?` 回复 ${replySecond.replyToUserName}${replySecond.replyToGroupName ? '(' +
                      replySecond.replyToGroupName + ')': ''}`
                      :'' }}
                      <!-- replyType: 1:顶级评论(根评论，不是回复) 2:直接回复(对顶级评论的回复，不显示"回复XX") 3:间接回复(对回复的回复，显示"用户X 回复 用户Y") -->
                    </div>
                    <!-- <div class="content">:&nbsp;{{ replySecond.content }}</div> -->
                    <div class="content">:&nbsp;<span
                        v-html="markMention(replySecond.content,replySecond.mentions)"></span></div>
                  </div>

                  <template v-if="replySecond.fileList && replySecond.fileList.length>0">
                    <div class="file-list" v-for="itemInside in replySecond.fileList" :key="itemInside.key"
                      :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
                      <!-- 图片类型 -->
                      <template v-if="isImageType(itemInside.type)">
                        <el-image class="img-preview" style="width: 100px; height: 100px"
                          :src="buildFileUrl(itemInside.url)" :preview-src-list="[buildFileUrl(itemInside.url)]">
                        </el-image>
                      </template>

                      <!-- 视频类型 -->
                      <template v-else-if="isVideoType(itemInside.type)">
                        <video style="width: 100%; height: auto;max-height: 20em;" :controls="videoControls">
                          <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                          您的浏览器不支持 video 标签
                        </video>
                      </template>

                      <!-- 其他类型 -->
                      <template v-else>
                        <div class="file-preview">
                          <i class="el-icon-document"></i>
                          <span>{{ itemInside.name }}</span>
                        </div>
                      </template>
                    </div>
                  </template>
                  <div class="comment-bottom">
                    <div class="createTime">{{replySecond.createdTime}}</div>
                    <div class="option-icon">
                      <img src="@/assets/images/simulatedVS/commentIcon.png" alt="" style="margin-right: 1.5em;"
                        @click="$emit('comment', replySecond)">
                      <img
                        :src="require(replySecond.isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
                        alt="" @click="$emit('like', replySecond)">
                      <span class="text">{{ replySecond.likeCount||0 }}</span>
                    </div>
                  </div>
                </div>

                <img class="team-point" v-if="replySecond.teamType==2"
                  src="@/assets/images/simulatedVS/teamPointBlue.png" alt="">
                <img class="team-point" v-else-if="replySecond.teamType==1"
                  src="@/assets/images/simulatedVS/teamPointRed.png" alt="">
                <div class="team-point" v-else></div>
              </div>
            </div>

            <!-- 回复操作按钮区域 -->
            <div class="reply-actions" style="padding: 0.5em 0em 0em 4em;">
              <!-- 展开更多回复按钮 -->
              <el-button v-if="shouldShowLoadMoreButton(replyItem)" class="action-btn load-more-btn" type="text"
                :disabled="replyItem.isLoadingMoreReplies" @click="loadMoreReplies(replyItem)">
                {{ replyItem.isLoadingMoreReplies ? '加载中...' : `展开更多` }}
              </el-button>

              <!-- 收起/展开回复按钮 -->
              <el-button type="text" class="action-btn collapse-btn" @click="toggleRepliesCollapse(replyItem)">
                {{ replyItem.isRepliesCollapsed ? '展开回复' : '收起' }}
              </el-button>
            </div>
          </template>
        </div>

        <!-- 滚动加载提示 -->
        <div v-if="hasMoreComments && commentLoading" class="scroll-loading-tip">
          <i class="el-icon-loading"></i>
          <span>正在加载更多评论...</span>
        </div>

        <!-- 没有更多评论提示 -->
        <div v-if="!hasMoreComments && displayComments.length > 0" class="no-more-tip">
          没有更多评论了
        </div>
      </div>
    </div>

    <!-- AI对抗对话框 -->
    <el-dialog :visible.sync="AIFightingDialog" class="ai-dialog" width="500px" append-to-body>
      <div slot="title" style="display: flex;align-items: center;font-size: 1.5em;">
        <img style="width: 1.5em;margin-right: 0.3em;" src="@/assets/images/simulatedVS/AI-icon.png" alt="">对抗
      </div>
      
      <div class="ai-dialog-content">
        <div class="form-item">
          <div class="label">评论数量（条）:</div>
          <div class="input-box">
            <div class="team">
              <img src="@/assets/images/simulatedVS/blueRectangle.png" alt=""> 蓝队
              <el-input-number v-model="AIFightingForm.blueNum" controls-position="right" :min="1"
                :max="100"></el-input-number>条
            </div>
            <div class="team" style="margin-top: 20px">
              <img src="@/assets/images/simulatedVS/redRectangle.png" alt=""> 红队
              <el-input-number v-model="AIFightingForm.redNum" controls-position="right" :min="1"
                :max="100"></el-input-number>条
            </div>
          </div>
        </div>
        <div class="form-item">
          <div class="label">ai人员:</div>
          <div class="select-box">
            
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeAIDialog">取 消</el-button>
        <el-button type="primary" @click="submitAIFighting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { commentReplyQueryApi, commentReplyQueryByParentApi, startAIFrightApi } from "@/api/simulatedVS/index.js";
import { checkRole } from "@/utils/permission.js";
import { processCommentFiles, markMention } from "@/utils/simulatedVS.js";
import { time } from "echarts";

export default {
  name: 'PostTemplate',
  data() {
    return {
      markMention,
      // 分页查询的评论数据
      paginatedComments: [],
      // 当前排序方式：createdTime(时间) 或 likeCount(点赞数)
      currentSortBy: this.initialSortBy || 'createdTime',
      // 游标分页相关
      nextCursor: null,
      hasMoreComments: false,
      commentLoading: false,
      // 总评论数
      totalCommentCount: 0,
      // 是否已初始化查询
      hasInitialized: false,
      // 滚动监听相关
      scrollTimer: null,
      isScrollLoading: false,
      // 用于检测新评论的上一次回复ID列表
      lastReplyIds: [],
      // 正在AI对抗
      AIFighting: false,
      // AI对抗对话框
      AIFightingDialog: false,
      AIFightingForm: {
        redNum: undefined,
        blueNum: undefined
      },

      checkRole,
    }
  },
  computed: {
    // 从 item 对象获取头像，提供默认值和空值处理
    avatar() {
      return this.item?.avatar || '';
    },
    // 从 item 对象获取昵称，提供默认值和空值处理
    nickName() {
      return this.item?.nickName || '';
    },
    // 从 item 对象获取群组名称，提供默认值和空值处理
    groupName() {
      return this.item?.groupName || '';
    },
    // 从 item 对象获取创建时间，提供默认值和空值处理
    createTime() {
      return this.item?.createTime || '';
    },
    // 从 item 对象获取内容，提供默认值和空值处理
    content() {
      return this.item?.content || '';
    },
    // 从 item 对象获取团队类型，提供默认值和空值处理
    teamType() {
      return this.item?.teamType || 0;
    },
    // 从 item 对象获取文件列表，提供默认值和空值处理
    fileList() {
      return this.item?.fileList || [];
    },
    // 从 item 对象获取点赞状态，提供默认值和空值处理
    isLike() {
      return this.item?.isLike || false;
    },
    // 从 item 对象获取点赞数量，提供默认值和空值处理
    likeCount() {
      return this.item?.likeCount || 0;
    },

    // 实际评论数量（优先显示真实数量）
    actualCommentCount() {
      if (this.paginatedComments.length > 0) {
        // 使用分页数据时，优先使用服务器返回的总数
        // 如果总数为0或未设置，则使用合并后的实际显示数量
        if (this.totalCommentCount > 0) {
          return this.totalCommentCount;
        } else {
          const mergedComments = this.mergeCommentsData();
          return this.deduplicateComments(mergedComments).length;
        }
      }
      // 都没有数据时，使用存储的总数
      return this.totalCommentCount;
    },
    // 显示评论区的条件
    showCommentBlock() {
      return this.paginatedComments.length > 0 || this.totalCommentCount > 0;
    },
    // 显示的评论列表（合并分页数据和WebSocket推送的新数据）
    displayComments() {
      // 如果有分页数据，优先使用分页数据
      if (this.paginatedComments.length > 0) {
        // 合并WebSocket推送的新评论和分页查询的评论
        const mergedComments = this.mergeCommentsData();
        return this.deduplicateComments(mergedComments);
      }
      // 没有任何数据时返回空数组
      return [];
    },
    videoControls() {
      return this.checkRole(['moderator']);
    },
    userId() {
      return this.$store.state.user.userId
    },
  },
  watch: {
    // 监听item变化，当渲染到这个评论时进行初始化查询
    item: {
      immediate: true,
      handler(newItem, oldItem) {
        // 如果commentId发生变化，重置初始化状态
        if (oldItem?.commentId !== newItem?.commentId) {
          this.hasInitialized = false;
          this.paginatedComments = [];
          this.nextCursor = null;
          this.hasMoreComments = false;
        }

        // 延迟检查，确保其他props也已更新
        this.$nextTick(() => {
          this.checkAndInitialize();
        });
      }
    },
    // 监听drillTaskId变化，重新初始化
    drillTaskId: {
      immediate: true,
      handler(newVal, oldVal) {
        // 如果drillTaskId发生变化，重置初始化状态
        if (oldVal !== newVal && oldVal !== undefined) {
          this.hasInitialized = false;
          this.paginatedComments = [];
          this.nextCursor = null;
          this.hasMoreComments = false;
        }

        // 延迟检查，确保其他props也已更新
        this.$nextTick(() => {
          this.checkAndInitialize();
        });
      }
    },


    // 监听分页评论数据变化，确保滚动监听器绑定
    paginatedComments: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length > 0 && (!oldVal || oldVal.length === 0)) {
          this.$nextTick(() => {
            this.bindScrollListener();
          });
        }
      }
    },

    // 监听显示评论区的条件变化
    showCommentBlock: {
      handler(newVal, oldVal) {
        if (newVal && !oldVal) {
          this.$nextTick(() => {
            this.bindScrollListener();
          });
        }
      }
    },

    // 监听初始排序方式变化
    initialSortBy: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal !== this.currentSortBy) {
          this.currentSortBy = newVal;
        }
      }
    }
  },
  props: {
    // 主要数据对象，包含所有帖子相关信息
    item: {
      type: Object,
      default: () => ({})
    },
    // 配置类属性，用于自定义组件行为
    imageTypes: {
      type: Array,
      default: () => ['png', 'jpeg', 'jpg']
    },
    videoTypes: {
      type: Array,
      default: () => ['mp4', 'avi', 'rmvb', 'mov', 'mkv']
    },
    // 业务相关配置
    drillTaskId: {
      type: [String, Number],
      default: ''
    },
    processStageId: {
      type: [String, Number],
      default: ''
    },
    // 初始排序方式
    initialSortBy: {
      type: String,
      default: 'createdTime'
    }
  },
  mounted() {
    // 延迟检查初始化条件，确保所有props都已传递
    this.$nextTick(() => {
      setTimeout(() => {
        this.checkAndInitialize();
      }, 100);
    });

    // 监听点赞更新事件
    this.$root.$on('comment-like-update', this.handleLikeUpdate);

    // 监听新评论添加事件
    this.$root.$on('comment-reply-added', this.handleCommentReplyAdded);
  },
  beforeDestroy() {
    // 清理滚动监听
    const commentBlock = this.$el?.querySelector('.comment-block');
    if (commentBlock && commentBlock.dataset.scrollListenerBound) {
      commentBlock.removeEventListener('scroll', this.handleCommentScroll);
      delete commentBlock.dataset.scrollListenerBound;
    }
    // 清理定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
    // 清理事件监听
    this.$root.$off('comment-like-update', this.handleLikeUpdate);
    this.$root.$off('comment-reply-added', this.handleCommentReplyAdded);
  },
  methods: {
    // 判断是否是当前允许的图片类型
    isImageType(fileType) {
      return this.imageTypes.includes(fileType)
    },
    // 判断是否是当前允许的视频类型
    isVideoType(fileType) {
      return this.videoTypes.includes(fileType)
    },
    // 获取视频MIME类型
    getVideoMimeType(ext) {
      const types = {
        mp4: 'video/mp4',
        avi: 'video/x-msvideo',
        mov: 'video/quicktime',
        mkv: 'video/x-matroska',
        rmvb: 'application/vnd.rn-realmedia-vbr'
      }
      return types[ext] || 'video/*'
    },
    buildFileUrl(url) {
      return process.env.VUE_APP_BASE_API + url;
    },
    // 添加头像方法
    getAvatar(avatar) {
      return avatar ? process.env.VUE_APP_BASE_API + avatar : require("@/assets/images/profile.jpg");
    },

    // 检查并初始化
    checkAndInitialize() {
      // 检查是否满足初始化条件
      if (this.item?.commentId && this.drillTaskId && !this.hasInitialized) {
        // 满足初始化条件，开始初始化评论查询
        this.initializeCommentQuery();
      } else {
        // 即使不需要初始化查询，也要绑定滚动监听器
        this.bindScrollListener();
      }
    },

    // 绑定滚动监听器
    bindScrollListener() {
      // 等待DOM更新
      this.$nextTick(() => {
        const commentBlock = this.$el.querySelector('.comment-block');

        if (commentBlock) {
          // 检查是否已经绑定过
          if (!commentBlock.dataset.scrollListenerBound) {
            commentBlock.addEventListener('scroll', this.handleCommentScroll, { passive: true });
            commentBlock.dataset.scrollListenerBound = 'true';
          }
        } else {
          // 如果还没找到，延迟重试
          setTimeout(() => {
            this.bindScrollListener();
          }, 500);
        }
      });
    },

    // 初始化评论查询
    async initializeCommentQuery() {
      if (!this.item?.commentId || !this.drillTaskId) {
        return;
      }

      if (this.hasInitialized) {
        return;
      }

      this.hasInitialized = true;
      await this.queryCommentReplies(true);

      // 查询完成后绑定滚动监听器
      this.bindScrollListener();
    },

    // 查询评论回复
    async queryCommentReplies(isReset = false) {
      if (this.commentLoading) return;

      try {
        this.commentLoading = true;

        const params = {
          drillTaskId: this.drillTaskId,
          // processStageId: this.processStageId,
          commentId: this.item.commentId,
          cursor: isReset ? null : this.nextCursor,
          pageSize: 20,
          orderBy: this.currentSortBy,
          sortDirection: 'desc',
          includeTotalCount: true,// 结果是否包含 totalCount
          replyLimit: 3, //顶级评论的回复显示数量限制（默认3条）
        };

        const response = await commentReplyQueryApi(params);

        if (response.code === 200 && response.data) {
          const { list: commentReplies, nextCursor, hasNext, actualSize, totalCount } = response.data;

          // 处理评论回复的文件
          const processedReplies = await processCommentFiles(commentReplies || [], this.videoTypes);

          if (isReset) {
            this.paginatedComments = processedReplies;
            // 优先使用 totalCount，如果没有则使用 actualSize
            this.totalCommentCount = totalCount || actualSize || 0;
          } else {
            // 合并数据并去重
            const combinedComments = [...this.paginatedComments, ...processedReplies];
            console.log('合并后的评论数据:', combinedComments);
            this.paginatedComments = this.deduplicateComments(combinedComments);
            console.log('去重后的评论数据:', this.paginatedComments);
          }

          this.nextCursor = nextCursor;
          this.hasMoreComments = hasNext;
        }
      } catch (error) {
        console.error('查询评论回复失败:', error);
        this.$message?.error('加载评论失败，请稍后重试');
      } finally {
        this.commentLoading = false;
      }
    },

    // 评论数据去重
    deduplicateComments(comments) {
      if (!comments || comments.length === 0) return [];

      const seen = new Set();
      return comments.filter(comment => {
        // 使用 commentReplyId 或 id 作为唯一标识
        const uniqueId = comment.commentReplyId || comment.id;
        if (!uniqueId) {
          return true; // 保留没有ID的评论，但可能导致重复
        }

        if (seen.has(uniqueId)) {
          return false;
        }

        seen.add(uniqueId);
        return true;
      });
    },

    // 处理新评论添加
    async handleNewCommentAdded(newComments) {
      console.log('处理新评论:', newComments);

      if (!newComments || newComments.length === 0) {
        console.log('❌ 没有新评论，退出处理');
        return;
      }

      console.log('✅ 检测到新评论，开始处理:', newComments.map(c => c.commentReplyId));

      // 处理新评论的文件
      const processedNewComments = await processCommentFiles(newComments, this.videoTypes);

      // 去重处理：确保不会重复添加已存在的评论
      const existingIds = new Set(this.paginatedComments.map(c => c.commentReplyId));
      const uniqueNewComments = processedNewComments.filter(c => !existingIds.has(c.commentReplyId));

      if (uniqueNewComments.length > 0) {

        uniqueNewComments.map(commentItem => {
          // 将评论的回复添加到列表中
          if (commentItem.replyType == '2' || commentItem.replyType == '3') {

            const commentIndex = this.paginatedComments.findIndex(
              // 用rootId查询到对应的一级评论
              comment => comment.commentReplyId === commentItem.rootId
            );

            if (commentIndex > -1) {
              // 确保children数组存在，如果不存在则初始化为空数组
              if (!this.paginatedComments[commentIndex].children) {
                this.$set(this.paginatedComments[commentIndex], 'children', []);
              }

              // 获取当前一级评论的二级回复列表
              const existingChildren = this.paginatedComments[commentIndex].children;

              // 检查当前回复是否已经存在于二级回复中（去重处理）
              const isDuplicate = existingChildren.some(child =>
                child.commentReplyId === commentItem.commentReplyId
              );

              if (!isDuplicate) {
                // 如果不是重复回复，则添加到children数组的开头（最新回复在前）
                this.$set(this.paginatedComments[commentIndex], 'children', [commentItem, ...existingChildren]);
                console.log('成功添加新的二级回复:', commentItem.commentReplyId);

                // 更新总评论数（二级回复也算作评论）
                this.totalCommentCount += 1;
              } else {
                console.log('二级回复已存在，跳过添加:', commentItem.commentReplyId);
              }
            } else {
              console.log('未发现该一级评论');
            }

          }
          // 将评论添加到列表中
          else {
            if (this.currentSortBy === 'createdTime') {
              // 按时间排序，将新评论添加到顶部
              this.paginatedComments = [commentItem, ...this.paginatedComments];
            } else {
              // 按点赞排序，添加后重新排序
              this.paginatedComments = [commentItem, ...this.paginatedComments].sort((a, b) => {
                const likeCountA = a.likeCount || 0;
                const likeCountB = b.likeCount || 0;
                return likeCountB - likeCountA; // 降序：点赞多的在前
              });
            }
            this.totalCommentCount += 1
          }
        });

      } else {
        console.log('⚠️ 所有新评论都已存在，跳过添加');
      }
    },

    // 切换排序方式
    async changeSortBy(sortBy) {
      // 无论是否相同，都重新查询以获取最新数据

      // 不同排序方式，切换并查询
      if (this.currentSortBy !== sortBy) {
        this.currentSortBy = sortBy;

        // 向父组件发送排序变化事件
        this.$emit('sort-change', {
          commentId: this.item?.commentId,
          sortBy: sortBy
        });
      }

      // 清空现有数据，确保重新开始
      // this.paginatedComments = []; //配合v-loading='commentLoading'，避免切换排序方式时评论模块闪烁
      this.nextCursor = null;
      this.hasMoreComments = false;
      // 重置总评论数，避免显示过时的数量
      this.totalCommentCount = 0;

      // 重新查询数据
      await this.queryCommentReplies(true);
      this.$refs['commentBlockRef']?.scrollTo({ top: 0, behavior: 'instant' });//滚动到顶部

      // 重新绑定滚动监听器（因为DOM可能重新渲染）
      this.bindScrollListener();
    },

    // 加载更多评论
    async loadMoreComments() {
      if (!this.hasMoreComments || this.commentLoading || this.isScrollLoading) {
        return;
      }

      this.isScrollLoading = true;
      try {
        await this.queryCommentReplies(false);
      } catch (error) {
        console.error('加载更多评论失败:', error);
      } finally {
        this.isScrollLoading = false;
      }
    },

    // 处理评论区滚动事件
    handleCommentScroll(e) {
      // 确保事件来源正确
      if (!e || !e.target) {
        return;
      }

      const container = e.target;

      // 验证容器是否为评论区域
      if (!container.classList.contains('comment-block')) {
        return;
      }

      // 清除之前的定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }

      // 防抖处理，减少到100ms提高响应速度
      this.scrollTimer = setTimeout(() => {
        try {
          const { scrollTop, scrollHeight, clientHeight } = container;

          // 防止无效数据
          if (scrollHeight <= 0 || clientHeight <= 0) {
            return;
          }

          // 计算距离底部的距离
          const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

          // 多种触发条件，提高触发概率
          const isNearBottom = distanceFromBottom <= 50; // 增加容差到50px
          const isAtBottom = distanceFromBottom <= 5;    // 严格的底部检测
          const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
          const isNearBottomByPercentage = scrollPercentage >= 0.9; // 滚动到90%时触发

          // 使用多种条件判断是否应该加载
          const shouldLoad = (isNearBottom || isAtBottom || isNearBottomByPercentage) &&
            this.hasMoreComments && !this.commentLoading && !this.isScrollLoading;

          if (shouldLoad) {
            this.loadMoreComments();
          }
        } catch (error) {
          console.error('滚动检测出错:', error);
        }
      }, 100); // 减少防抖时间到100ms
    },

    // 处理点赞更新事件
    handleLikeUpdate(likeData) {
      const { commentId, commentReplyId, commentType, likeCount, isLike, userId } = likeData;
      const isInAmplifyDialog = this.$el?.closest('.amplify-dialog') !== null;

      // 只处理属于当前评论的点赞更新
      if (this.item?.commentId !== commentId) {
        return;
      }

      if (commentType == 7) {
        // 回复评论的点赞更新
        this.updateReplyLikeCount(likeData);
      } else {
        // 主评论的点赞更新
        this.updateMainCommentLikeCount(likeData);
      }
    },

    // 处理新评论添加事件
    async handleCommentReplyAdded({ commentId, replyData }) {
      const isInAmplifyDialog = this.$el?.closest('.amplify-dialog') !== null;

      // 只处理属于当前评论的新回复
      if (this.item?.commentId !== commentId) {
        return;
      }

      // 处理新评论（复用现有的处理逻辑）
      await this.handleNewCommentAdded([replyData]);
    },

    // 更新回复评论的点赞数和状态
    updateReplyLikeCount(likeData) {
      const { commentReplyId, likeCount, isLike, userId, parentId, rootId } = likeData;

      // 更新分页评论数据
      if (this.paginatedComments.length == 0) {
        return console.log('分页评论数据为空，跳过更新');
      }

      // 根据rootId找到第一级评论
      const commentIndex = this.paginatedComments.findIndex(
        comment => comment.commentReplyId === rootId
      );

      if (commentIndex == -1) {
        return console.log('未找到对应的一级评论');
      }

      // 将二三级评论的点赞更新到列表中
      if (parentId != '0') {
        // 确保children数组存在，如果不存在则初始化为空数组
        if (!this.paginatedComments[commentIndex].children || this.paginatedComments[commentIndex].children.length == 0) {
          return console.log('children数组不存在，跳过更新');
        }

        // 获取当前一级评论的二级回复列表
        const existingChildren = this.paginatedComments[commentIndex].children;

        // 找到对应的二三级回复
        const replyIndex = existingChildren.findIndex(
          reply => reply.commentReplyId === commentReplyId
        );

        if (replyIndex > -1) {
          this.$set(existingChildren[replyIndex], 'likeCount', likeCount);
          if (userId === this.userId) {
            this.$set(existingChildren[replyIndex], 'isLike', isLike);
          }
        } else {
          console.log('未找到对应的二级回复');
        }
      }
      // 将一级评论的点赞更新到列表中
      else {
        this.$set(this.paginatedComments[commentIndex], 'likeCount', likeCount);
        if (userId === this.userId) {
          this.$set(this.paginatedComments[commentIndex], 'isLike', isLike);
        }
      }
    },

    // 更新主评论的点赞数和状态
    updateMainCommentLikeCount(likeData) {
      const { likeCount, isLike } = likeData;
      // console.log('🔄 更新主评论点赞数和状态:', { likeCount, isLike });
      // 主评论的点赞数和状态更新由父组件处理，这里主要是为了完整性
      // 如果需要在PostTemplate中也更新主评论数据，可以在这里添加逻辑
    },

    // 合并分页数据和WebSocket推送的新数据
    mergeCommentsData() {
      // 直接返回分页数据（新评论已通过事件机制处理）
      return this.paginatedComments;
    },

    // 判断是否应该显示"展开更多回复"按钮
    shouldShowLoadMoreButton(replyItem) {
      if (!replyItem || replyItem.isRepliesCollapsed) {
        return false;
      }

      return replyItem.childrenPage.hasNext;
    },

    // 切换回复的收起/展开状态
    toggleRepliesCollapse(replyItem) {
      if (!replyItem) return;

      // 使用 Vue.set 确保响应式更新
      this.$set(replyItem, 'isRepliesCollapsed', !replyItem.isRepliesCollapsed);
    },

    // 加载更多回复
    async loadMoreReplies(replyItem) {
      if (!replyItem || replyItem.isLoadingMoreReplies) {
        return;
      }

      try {
        // 设置加载状态
        this.$set(replyItem, 'isLoadingMoreReplies', true);

        const params = {
          drillTaskId: this.drillTaskId,
          topLevelCommentId: replyItem.commentReplyId,
          // processStageId: this.processStageId,
          cursor: replyItem.childrenPage?.nextCursor || replyItem.children?.[replyItem.children.length - 1]?.commentReplyId || null,
          pageSize: 10,
          orderBy: this.currentSortBy,
          sortDirection: 'desc'
        };

        const response = await commentReplyQueryByParentApi(params);

        if (response.code === 200 && response.data) {
          const { list, nextCursor, hasNext } = response.data;

          if (list && list.length > 0) {
            // 处理新获取的回复文件
            const processedReplies = await processCommentFiles(list, this.videoTypes);

            // 确保 children 数组存在
            if (!replyItem.children) {
              this.$set(replyItem, 'children', []);
            }

            // 合并新的回复数据，避免重复
            const existingIds = new Set(replyItem.children.map(child => child.commentReplyId));
            const uniqueNewReplies = processedReplies.filter(reply => !existingIds.has(reply.commentReplyId));

            if (uniqueNewReplies.length > 0) {
              // 将新回复添加到现有回复列表中
              replyItem.children.push(...uniqueNewReplies);
            }

            // 更新游标和状态
            this.$set(replyItem.childrenPage, 'nextCursor', nextCursor);
            this.$set(replyItem.childrenPage, 'hasNext', hasNext);
          }
        }
      } catch (error) {
        console.error('加载更多回复失败:', error);
        this.$message?.error('加载更多回复失败，请稍后重试');
      } finally {
        // 清除加载状态
        this.$set(replyItem, 'isLoadingMoreReplies', false);
      }
    },
    // 点击停止AI对抗
    handleStopAI() {
      this.AIFighting = false;
    },
    // 点击开始AI对抗
    handleStartAI() {
      this.AIFightingDialog = true;
    },
    // 提交AI对抗
    submitAIFighting() {
      let queryForm =  {
          redNum: this.AIFightingForm.redNum,
          blueNum: this.AIFightingForm.blueNum
        }

      // startAIFrightApi(queryForm).then(res => {
      //   if (res.code == 200) {
      //     this.$message.success('AI对抗开始')
      //     this.closeAIDialog()
      //     this.AIFighting = true;
      //   }
      // })
    },
    closeAIDialog() {
      this.AIFightingDialog = false;
      this.AIFightingForm = {}
    },
  }
}
</script>

<style scoped lang="scss">
.template-bltw {
  // font-size: 0.8em;
  font-size: 1.1em;
  background-color: #FFFFFF;

  .post-block {
    font-size: 12em;
    display: flex;
    padding: 1em;
    padding-bottom: 0;

    >img {
      width: 3em;
      height: 3em;
      aspect-ratio: 1;
      flex-shrink: 0;
      margin-right: 1em;
      border-radius: 50%;
    }

    .main-content {
      color: #000000;
      width: calc(100% - 6em);

      .user-name {
        // margin-bottom: 0.5em;
        display: flex;
        align-items: center;

        .text {
          font-weight: 600;
          // filter: blur(0.15em);
          margin-right: 1em;
        }

        >img {
          width: 1.5em;
          height: 1.5em;
          aspect-ratio: 1;
          flex-shrink: 0;
        }

        .AI-btn {
          border-radius: 16px;
          padding: 0.2em 0.7em;
          display: flex;
          align-items: center;
          margin-right: 1em;
          cursor: pointer;
          background: #E8F1FF;
          border: 1px solid #237CFF;

          .AI-icon {
            width: 1em;
            margin-right: 0.2em;
          }

          .play-icon {
            width: 1.2em;
            height: 1.2em;
            margin-left: 0.4em;
          }
        }

        .fighting {
          background: #FFDDDD;
          border: 1px solid #F60000;
        }
      }

      .content {
        margin-bottom: 0.5em;
        word-break: break-word;
        white-space: pre-wrap;
      }

      .file-list {
        display: inline-block;

        .img-preview {
          width: 10em;
          height: 10em;
          margin-right: 1em;
        }
      }

      .bottom-icon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 1em;
        margin-bottom: 1em;

        .icon-item {
          display: flex;
          align-items: center;

          span {
            color: #666666;
          }

          img {
            width: 1.4em;
            margin-right: 0.4em;
            cursor: pointer;
          }
        }
      }
    }
  }

  .comment-block-top {
    width: 100%;
    border-bottom: 0.01em solid #EEEEEE;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0em 12em;

    .top-left {
      color: #333333;
      font-size: 14em;
      font-weight: 600;
      border-bottom: 0.2em solid #EB7350;
      padding: 0.4em;
      margin-bottom: 0.1em;
      width: fit-content;
    }

    .sort-buttons {
      display: flex;
      gap: 5em;

      .sort-btn {
        padding: 0.3em 0.8em;
        border: 0.1em solid #ddd;
        background: #fff;
        color: #666;
        border-radius: 0.3em;
        cursor: pointer;
        font-size: 12em;
        // transition: all 0.3s;

        &:hover {
          border-color: #247CFF;
          color: #247CFF;
        }

        &.active {
          background: #247CFF;
          border-color: #247CFF;
          color: #fff;
        }
      }
    }
  }

  .comment-block {
    max-height: 350em;
    overflow-y: auto;
    overflow-x: hidden;
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;

    /* Webkit浏览器滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #ccc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #999;
    }

    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1em;
      color: #666;
      font-size: 12em;

      i {
        margin-right: 0.5em;
      }
    }

    .scroll-loading-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1em;
      color: #666;
      font-size: 12em;

      i {
        margin-right: 0.5em;
        animation: rotate 1s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }

        to {
          transform: rotate(360deg);
        }
      }
    }

    .no-more-tip {
      text-align: center;
      padding: 1em;
      color: #999;
      font-size: 12em;
    }

    .comment-item {
      font-size: 12em;
      background-color: #FFFFFF;
      padding: 0.5em 1em;

      .top-comment {
        display: flex;
      }

      .commentInComment {
        display: flex;
        padding: 1em 0em 0em 4em;
      }

      .reply-actions {
        display: flex;
        gap: 1em;
        align-items: center;

        .action-btn {
          background: none;
          border: none;
          color: #247CFF;
          cursor: pointer;
          font-size: 1em;
          padding: 0.3em 0.6em;
          border-radius: 0.3em;
          transition: all 0.2s ease;

          &:hover {
            // background-color: rgba(36, 124, 255, 0.1);
          }

          &:disabled {
            color: #999;
            cursor: not-allowed;
            background-color: transparent;
          }
        }
      }

      .avatarImg {
        width: 3em;
        height: 3em;
        aspect-ratio: 1;
        flex-shrink: 0;
        border-radius: 50%;
      }

      .main-content {
        color: #000000;
        padding: 0 1em;
        flex: 1;

        .user-name {
          // color: #646464;
          color: #eb7350;
          // filter: blur(0.15em);
          display: inline-block;
          margin-right: 1em;
        }

        .content {
          margin-bottom: 0.5em;
          word-break: break-word;
          white-space: pre-wrap;
          display: inline;
        }

        .file-list {
          display: inline-block;

          .img-preview {
            width: 10em;
            height: 10em;
            margin-right: 1em;
          }
        }

        .comment-bottom {
          display: flex;
          align-items: end;
          justify-content: space-between;

          .option-icon {
            display: flex;
            align-items: center;

            >img {
              width: 1.3em;
              margin-right: 0.5em;
              cursor: pointer;
            }

            .text {
              color: #999999;
            }
          }
        }
      }

      .team-point {
        width: 1.5em;
        height: 1.5em;
        aspect-ratio: 1;
        flex-shrink: 0;
        margin-right: 1em;
      }
    }
  }

  .createTime {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-size: 0.9em;
    color: #999999;
  }
}

.ai-dialog {
  .top-tip {
    color: red;
    display: flex;
    align-items: center;

    >i {
      font-size: 1.5em;
    }
  }

  .ai-dialog-content {
    padding: 20px 0;

    .form-item {
      display: flex;
      align-items: center;

      .label {
        width: 8em;
        text-align: right;
        margin-right: 1em;
      }

      .input-box {
        .team {
          display: flex;
          align-items: center;
          gap: 0.5em;

          >img {
            height: 0.6em;
          }
        }
      }
      .select-box {
        border: 1px solid #ccc;
        border-radius: 3px;
        flex: 1;
      }
    }
  }
}
</style>
