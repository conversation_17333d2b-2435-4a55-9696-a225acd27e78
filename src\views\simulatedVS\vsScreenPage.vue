<template>
    <div class="vsConsolePage">
        <VsScreen ref="vsScreen" style="height: calc(100% - 0px);" :drillTaskId="drillTaskId" :current-step="currentStep"
            :next-step="nextStep" :newCurrentStage="newCurrentStage" :detailInfo="detailInfo" :showInstruction="false" :loading="vsScreenLoading"></VsScreen>
    </div>
</template>

<script>
import { WS_EVENTS, safeOn } from '@/utils/eventBus'
import { drillProcessStartApi } from "@/api/simulatedVS/index.js";
import VsScreen from '@/views/simulatedVS/vsScreen.vue';
import { getTeamName } from '@/utils/index.js';
export default {
    components: { VsScreen },
    data() {
        return {
            currentStep: '',
            nextStep: '',
            newCurrentStage: {
                blueStageScore: '',
                redStageScore: '',
                scoreType: '',
            },
            detailInfo: {},
            vsScreenLoading: false, // VsScreen组件的loading状态
        }
    },
    computed: {
        drillTaskId() {
            return this.$route.query?.drillTaskId || ''
        },
    },
    created() {
        // 安全监听消息
        this.unlistenMessage = safeOn(WS_EVENTS.MESSAGE, this.handleMessage)
    },
    beforeDestroy() {
        // 清理所有监听
        this.unlistenMessage?.()
    },
    mounted() {
        if (this.$route.query?.drillTaskId) {
            this.queryDetailInfo()

        } else {
            this.$message.error('id不能为空')
        }
    },
    methods: {
        async queryDetailInfo(showLoading = true) {
            if (showLoading) {
                this.vsScreenLoading = true; // 开始loading
            }
            try {
                const res = await drillProcessStartApi({ drillTaskId: this.drillTaskId });
                if (res.code == '200') {
                    console.log('detailInfo', res.data)
                    this.detailInfo = {...res.data, teamName: getTeamName(res.data?.roleInfo||'')}
                    this.currentStep = res.data.currentStageId
                    this.nextStep = res.data.nextStageId
                    let stageList = res.data.drillProcessStageRes.filter(item => item.processStageId == res.data.currentStageId)
                    if(stageList.length>0) {
                        this.newCurrentStage = JSON.parse(JSON.stringify(stageList))[0] // 当前阶段信息
                    }else {
                        this.newCurrentStage = {}
                    }

                    // 等待VsScreen组件完成数据初始化
                    await this.$nextTick();
                    if (showLoading && this.$refs.vsScreen && this.$refs.vsScreen.waitForDataInit) {
                        await this.$refs.vsScreen.waitForDataInit();
                    }
                }
            } catch (error) {
                console.error('获取演练详情失败:', error);
                this.$message.error('获取演练详情失败，请重试');
            } finally {
                if (showLoading) {
                    this.vsScreenLoading = false; // 结束loading
                }
            }
        },
        handleMessage({ channel, data }) {
            if (channel === 'DRILL_COMMENT') { // 评论通知

            }
            if (channel === 'DRILL_STAGE') { // 阶段通知
                let { drillTaskId, currentStageId, nextStageId, newCurrentStage } = data
                if (drillTaskId != this.drillTaskId) {
                    return console.log('drillTaskId不一致')
                }
                this.currentStep = currentStageId
                this.nextStep = nextStageId
                this.newCurrentStage = newCurrentStage
                // if (!nextStageId) {//到最终总结阶段，更新detailInfo
                    // this.queryDetailInfo(false) // 阶段切换时不显示loading
                // }
            }
        },
    }
}
</script>
<style scoped lang="scss">
.vsConsolePage {
    width: 100%;
    height: 100vh;
}
</style>