<template>
  <div class="template-han">
    <div class="title">{{ title }}</div>
    <div class="driver">
      <div class="line-1"></div>
      <div class="line-2"></div>
    </div>
    <div class="text">{{ content }}</div>
  </div>
</template>

<script>
export default {
  name: 'LetterTemplate',
  props: {
    title: {
      type: String,
      default: '文件函'
    },
    content: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.template-han {
  font-size: 12em;
  background-color: #FFFFFF;
  margin: 0 10%;
  text-align: center;
  color: #000;

  img {
    width: 100%;
  }

  .title {
    font-size: 1.8em;
    text-align: center;
    font-weight: 600;
    color: #D93B4B;
    padding: 1em;
  }


  .driver{
    padding: 0 1em;
    .line-1 {
      width: 100%;
      height: 0.4em;
      background-color: #D93B4B;
    }

    .line-2 {
      width: 100%;
      height: 0.1em;
      background-color: #D93B4B;
      margin-top: 0.2em;
    }
  }

  .text {
    text-indent: 2ch;
    /* 首行缩进2个字符 */
    line-height: 1.5em;
    text-align: start;
    word-wrap: break-word;
    white-space: pre-wrap;
    min-height: 30em;
    padding: 1em;
    font-size: 1.2em;
  }
}
</style>
