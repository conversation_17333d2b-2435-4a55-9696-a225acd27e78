# MentionInput 艾特功能组件

## 📋 功能说明

MentionInput 是一个基于 tribute.js 的艾特功能组件，支持在文本输入框中艾特其他用户。

## 🎯 特性

- ✅ **艾特功能**: 输入 @ 符号触发用户选择
- ✅ **用户搜索**: 支持按用户名和昵称搜索
- ✅ **用户信息**: 显示用户头像、昵称和用户名
- ✅ **键盘导航**: 支持上下键选择，回车确认
- ✅ **样式适配**: 与 Element UI 风格保持一致
- ✅ **数据提取**: 提供获取艾特用户列表的方法
- ✅ **蓝色标签**: 艾特用户显示为蓝色的整体标签
- ✅ **整体删除**: 删除时整个艾特标签作为一个整体删除
- ✅ **光标控制**: 光标不会停留在艾特标签内部

## 📦 使用方法

### 1. 基本用法

```vue
<template>
  <mention-input 
    v-model="content"
    type="textarea"
    placeholder="请输入内容，使用 @ 可以艾特其他用户"
    :autosize="{ minRows: 4, maxRows: 6 }"
  />
</template>

<script>
import MentionInput from '@/components/MentionInput'

export default {
  components: {
    MentionInput
  },
  data() {
    return {
      content: ''
    }
  }
}
</script>
```

### 2. 在演练任务中使用

```vue
<template>
  <mention-input
    v-model="content"
    type="textarea"
    :drill-task-id="drillTaskId"
    placeholder="请输入评论，使用 @ 可以艾特其他用户"
  />
</template>

<script>
import MentionInput from '@/components/MentionInput'

export default {
  components: {
    MentionInput
  },
  data() {
    return {
      content: '',
      drillTaskId: '12345' // 当前演练任务ID
    }
  }
}
</script>
```

### 3. 获取艾特的用户

```vue
<template>
  <div>
    <mention-input
      ref="mentionInput"
      v-model="content"
      type="textarea"
      :drill-task-id="drillTaskId"
    />
    <el-button @click="handleSubmit">提交</el-button>
  </div>
</template>

<script>
export default {
  methods: {
    handleSubmit() {
      // 获取艾特的用户列表
      const mentionedUsers = this.$refs.mentionInput.getMentionedUsers()
      console.log('艾特的用户:', mentionedUsers)
      
      // 提交数据
      this.submitData({
        content: this.content,
        mentionedUsers: mentionedUsers
      })
    }
  }
}
</script>
```

## 🔧 Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 输入框的值 | String | '' |
| trigger | 艾特触发字符 | String | '@' |
| allowSpaces | 是否允许空格 | Boolean | false |
| searchDelay | 搜索延迟（毫秒） | Number | 300 |
| maxUsers | 最大显示用户数量 | Number | 10 |
| drillTaskId | 演练任务ID，用于过滤参与该任务的用户 | String/Number | '' |

其他 props 会透传给 el-input 组件。

## 📤 Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 输入框值改变时触发 | (value: string) |
| focus | 输入框获得焦点时触发 | (event: Event) |
| blur | 输入框失去焦点时触发 | (event: Event) |

## 🔨 Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| getMentionedUsers | 获取艾特的用户列表 | - | Array |
| insertMention | 插入艾特用户 | (user: Object) | - |

### getMentionedUsers 返回格式

```javascript
[
  {
    userName: 'zhangsan',
    nickName: '张三',
    userId: '123',
    position: 5,  // 在文本中的位置
    length: 8     // 艾特文本的长度
  }
]
```

## 🎨 样式定制

组件使用了全局样式来定制 tribute 容器的外观，如需修改样式，可以覆盖以下 CSS 类：

```css
.tribute-container {
  /* 容器样式 */
}

.mention-item {
  /* 用户项样式 */
}

.mention-avatar {
  /* 用户头像样式 */
}

.mention-name {
  /* 用户名称样式 */
}

.mention-username {
  /* 用户账号样式 */
}
```

## 📝 注意事项

1. **用户数据**:
   - 有 `drillTaskId` 时使用 `drillTaskUserApi` 获取任务参与用户
   - 无 `drillTaskId` 时使用 `getUsersForMention` 获取通用用户列表
2. **权限控制**: 确保用户有访问用户列表的权限
3. **网络异常**: 组件已处理网络请求异常情况
4. **性能优化**: 用户列表限制为50个，搜索结果限制为10个
5. **兼容性**: 支持 textarea 和 input 类型
6. **任务关联**: 当 `drillTaskId` 改变时会自动重新加载用户列表

## 🔄 更新日志

- **v2.0.0**: 重构为 contenteditable 实现，艾特用户显示为蓝色整体标签，支持整体删除
- **v1.1.0**: 添加任务用户过滤功能，支持 drillTaskId 参数
- **v1.0.0**: 初始版本，支持基本艾特功能
