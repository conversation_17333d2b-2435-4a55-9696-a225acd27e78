import request from "@/utils/request";

// 查询演练任务
export function drillTaskQueryApi(data) {
  return request({
    url: "/drill/task/query",
    method: "post",
    data,
  });
}
// 新增演练任务
export function drillTaskSaveApi(data) {
  return request({
    url: "/drill/task/save",
    method: "post",
    data,
  });
}
// 查询演练人员
export function drillTaskUserApi(data) {
  return request({
    url: "/drill/task/user",
    method: "post",
    data,
  });
}
// 演练默认阶段查询
export function drillDefaultStageApi(data) {
  return request({
    url: "/drill/stage/query",
    method: "post",
    data,
  });
}
// 根据任务id查询任务
export function drillTaskQueryOneApi(data) {
  return request({
    url: "/drill/task/query/one",
    method: "post",
    data,
  });
}
// 开始演练
export function drillProcessStartApi(data) {
  return request({
    url: "/drill/process/start",
    method: "post",
    data,
  });
}
// 下一阶段
export function drillStageNextApi(data) {
  return request({
    url: "/drill/stage/next",
    method: "post",
    data,
  });
}
// 发布指令或评论
export function drillCommentPublishApi(data) {
  return request({
    url: "/drill/comment/publish",
    method: "post",
    data,
  });
}
// 上传文件
export function fileUploadFileApi(data) {
  return request({
    url: "/file/uploadFile",
    method: "post",
    data,
  });
}
// 文件链接获取
export function fileGetUrlByIdsApi(data) {
  return request({
    url: "/file/getUrlByIds",
    method: "post",
    data,
  });
}
// 离开演练(中途离开，清除推送)
export function drillProcessLeaveApi(data) {
  return request({
    url: "/drill/process/leave",
    method: "post",
    data,
  });
}
// 结束演练
export function drillProcessEndApi(data) {
  return request({
    url: "/drill/process/end",
    method: "post",
    data,
  });
}
// 查询任务下指令或评论(阶段,指令类型,队伍类型)
export function drillCommentQueryApi(data) {
  return request({
    url: "/drill/comment/query",
    method: "post",
    data,
  });
}
// 查询任务下指令或评论(阶段,指令类型,队伍类型) 不分红蓝方
export function drillAllCommentQueryApi(data) {
  return request({
    url: "/drill/comment/query-all",
    method: "post",
    data,
  });
}
// 倒计时开始
export function drillTimerStartApi(data) {
  return request({
    url: "/drill/timer/start",
    method: "post",
    data,
  });
}
// 倒计时结束
export function drillTimerEndApi(data) {
  return request({
    url: "/drill/timer/end",
    method: "post",
    data,
  });
}

// 签到
export function drillSingUpApi(data) {
  return request({
    url: "/drill/sign/save",
    method: "post",
    data,
  });
}
// 任务演练签到查询
export function drillSingQueryApi(data) {
  return request({
    url: "/drill/sign/query",
    method: "post",
    data,
  });
}

// 点赞
export function commentLikeAddApi(data) {
  return request({
    url: "/comment/like/add",
    method: "post",
    data,
  });
}

// 取消点赞
export function commentLikeCancelApi(data) {
  return request({
    url: "/comment/like/cancel",
    method: "post",
    data,
  });
}

// 新增回复评论
export function commentReplyAddApi(data) {
  return request({
    url: "/comment/reply/add",
    method: "post",
    data,
  });
}

// 查询专家点评
export function drillScoreQueryApi(data) {
  return request({
    url: "/drill/score/query",
    method: "post",
    data,
  });
}

// 专家点评统计
export function drillScoreStatisticsApi(data) {
  return request({
    url: "/drill/score/statistics",
    method: "post",
    data,
  });
}

// 演练人员注册
export function registerApi(data) {
  return request({
    url: "/drill/user/register",
    method: "post",
    data,
  });
}

// 评论回复查询（游标分页）
export function commentReplyQueryApi(data) {
  return request({
    url: "/comment/reply/query",
    method: "post",
    data,
  });
}

// 回复评论展开更多（游标分页）
export function commentReplyQueryByParentApi(data) {
  return request({
    url: "/comment/reply/queryByParent",
    method: "post",
    data,
  });
}

// 注册查询分组
export function availableGroupsApi(data) {
  return request({
    url: "/drill/user/available-groups",
    method: "post",
    data,
  });
}

// 查询任务信息
export function getTaskInfoApi(data) {
  return request({
    url: "/drill/user/task-info",
    method: "post",
    data,
  });
}

// 点赞评论数查询
export function getTaskStagesStatisticsApi(data) {
  return request({
    url: "/drill/statistics/task-stages",
    method: "post",
    data,
  });
}

// 得分查询
export function getTaskScoresStatisticsApi(data) {
  return request({
    url: "/drill/statistics/task-scores",
    method: "post",
    data,
  });
}

// 获取可@的用户
export function getUsersForMention(data) {
  return request({
    url: "/drill/users/search",
    method: "post",
    data,
  });
}

// ==================== 通知相关接口 ====================

// 查询通知列表
export function getNotificationListApi(data) {
  return request({
    url: "/drill/notifications/query",
    method: "post",
    data,
  });
}

// 标记通知为已读
export function markNotificationReadApi(data) {
  return request({
    url: "/drill/notifications/markRead",
    method: "post",
    data,
  });
}

// 获取通知统计信息
export function getNotificationStatisticsApi(data) {
  return request({
    url: "/drill/notifications/stats",
    method: "post",
    data,
  });
}

// AI一键生成衍生话题
export function AIRelatedTopicsApi(data) {
  return request({
    url: "/drill/ai/generate-related-topics",
    method: "post",
    data,
  });
}

// 开启AI对抗
export function startAIFrightApi(data) {
  return request({
    url: "/drill/notifications",
    method: "post",
    data,
  });
}