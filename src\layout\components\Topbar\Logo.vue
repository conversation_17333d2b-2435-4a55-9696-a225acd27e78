<template>
  <div class="sidebar-logo-container" :class="{ 'collapse': collapse }"
       :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="">
        <img :src="sideLogo ? sideLogo : logo" class="sidebar-logo">
        <!-- <h1 v-else class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.sidebarTitle : variables.sidebarLightTitle }">{{ title }} </h1> -->
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="">
        <img :src="sideLogo ? sideLogo : logo" class="sidebar-logo">
        <h1 class="sidebar-title" :style="{ color: '#fff' }">{{ titleName || '博约舆情' }} </h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoImg from '@/assets/logo/logo.png'
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    },
    sideLogo() {
      return this.$store.state.user.titleLogo
    },
    titleName() {
      return this.$store.state.user.titleName
    },
  },
  data() {
    return {
      title: '博约舆情',
      logo: logoImg
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 80px;
  line-height: 80px;
  background: linear-gradient(180deg, #3485FF 0%, #0066FF 100%);
  color: #fff;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 41px;
      // height: 46px;
      vertical-align: middle;
      margin-right: 14px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 48px;
      font-size: 32px;
      // font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      width: 188px;
      margin-right: 0px;
    }
  }
}

/* 手机竖屏模式 */
@media screen and (max-width: 992px) and (orientation: portrait) {
  .sidebar-logo-container .sidebar-logo-link .sidebar-title {
    display: none;
  }
}
</style>
