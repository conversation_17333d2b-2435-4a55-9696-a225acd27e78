<template>
  <div class="mention-input-wrapper">
    <!-- 使用 contenteditable 实现可编辑区域 -->
    <div ref="editableDiv" class="mention-editable"
      :class="{
        'is-textarea': $attrs.type === 'textarea',
        'is-exceed': isExceed
      }"
      contenteditable="true"
      :placeholder="$attrs.placeholder || '请输入内容'"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
      @paste="handlePaste">
    </div>

    <!-- 字数统计 -->
    <div v-if="showWordLimit && isLimitLength" class="mention-count">
      <span :class="{ 'is-exceed': isExceed }">{{ textLength }}</span>
      <span>/{{ upperLimit }}</span>
    </div>
  </div>
</template>

<script>
import Tribute from 'tributejs'
import { getUsersForMention } from '@/api/simulatedVS'

export default {
  name: 'MentionInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    // 艾特触发字符
    trigger: {
      type: String,
      default: '@'
    },
    // 是否允许空格
    allowSpaces: {
      type: Boolean,
      default: false
    },
    // 搜索延迟（毫秒）
    searchDelay: {
      type: Number,
      default: 300
    },
    // 最大显示用户数量
    maxUsers: {
      type: Number,
      default: 10
    },
    // 演练任务ID，用于过滤参与该任务的用户
    drillTaskId: {
      type: [String, Number],
      default: ''
    },
    // 最大输入长度，支持数字和字符串
    maxlength: {
      type: [Number, String],
      default: 0
    },
    // 是否显示字数统计
    showWordLimit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localValue: this.value,
      tribute: null,
      users: [],
      isLoading: false,
      searchDebounceTimer: null, // 搜索防抖定时器
      lastSearchKeyword: '' // 上次搜索的关键词
    }
  },
  computed: {
    // 转换为数字类型的长度限制
    upperLimit() {
      return Number(this.maxlength) || 0
    },
    // 是否有长度限制
    isLimitLength() {
      return this.upperLimit > 0
    },
    // 当前文本长度
    textLength() {
      return this.localValue ? this.localValue.length : 0
    },
    // 是否超出长度限制
    isExceed() {
      return this.isLimitLength && this.textLength > this.upperLimit
    }
  },
  watch: {
    value(newVal) {
      if (newVal !== this.localValue) {
        this.localValue = newVal
        this.setEditableContent()
      }
    },
    drillTaskId(newVal, oldVal) {
      // 当 drillTaskId 改变时，重新加载用户列表
      if (newVal !== oldVal) {
        this.loadUsers()
      }
    }
  },
  mounted() {
    this.loadUsers()
    this.initTribute()
    this.setEditableContent()
  },
  beforeDestroy() {
    if (this.tribute) {
      this.tribute.detach(this.getEditableElement())
    }

    // 清理防抖定时器
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer)
      this.searchDebounceTimer = null
    }
  },
  methods: {
    // 生成艾特标签HTML模板 - 统一的模板生成方法
    createMentionTag(user, displayName = null) {
      const name = displayName || user.nickName || user.userName
      return `<span class="mention-tag" contenteditable="false" data-user-id="${user.userId}" data-user-name="${user.userName}" title="${name}">@${name}</span>`
    },



    // 对艾特数据进行排序处理
    sortMentions(mentions) {
      // 按位置排序，保留所有艾特（包括重复的）
      return mentions.sort((a, b) => a.startPos - b.startPos)
    },

    // 获取当前已经艾特的用户ID列表
    getMentionedUserIds() {
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return []

      const mentionSpans = editableDiv.querySelectorAll('.mention-tag')
      const mentionedUserIds = new Set()

      mentionSpans.forEach(span => {
        const userId = span.getAttribute('data-user-id')
        if (userId) {
          mentionedUserIds.add(userId)
        }
      })

      return Array.from(mentionedUserIds)
    },

    // 处理艾特菜单项的点击事件
    handleMentionClick(event) {
      const mentionItem = event.target.closest('.mention-item')
      if (mentionItem && mentionItem.classList.contains('mentioned-disabled')) {
        // 阻止选择已艾特的用户
        event.preventDefault()
        event.stopPropagation()
        return false
      }
    },

    // 初始化 Tribute
    initTribute() {
      const editableElement = this.getEditableElement()
      if (!editableElement) return

      this.tribute = new Tribute({
        trigger: this.trigger,
        allowSpaces: this.allowSpaces,
        requireLeadingSpace: false, // 允许在文字后直接输入@触发
        noMatchTemplate: () => null,
        lookup: 'searchableText', // 使用包含搜索文本的字段进行匹配
        fillAttr: 'value', // 指定填充字段
        values: (text, cb) => {
          this.searchUsers(text, cb)
        },
        selectTemplate: (item) => {
          // 检查用户是否已经被艾特过，如果是则阻止选择
          const mentionedUserIds = this.getMentionedUserIds()
          if (mentionedUserIds.includes(item.original.userId)) {
            return false // 阻止选择
          }

          // 使用统一的模板生成方法
          if (this.tribute.range.isContentEditable(editableElement)) {
            return this.createMentionTag(item.original)
          }
          return `@${item.original.userName}`
        },
        menuItemTemplate: (item) => {
          const user = item.original
          const defaultAvatar = require("@/assets/images/profile.jpg")
          const avatarSrc = user.avatar || defaultAvatar

          // 检查用户是否已经被艾特过
          const mentionedUserIds = this.getMentionedUserIds()
          const isAlreadyMentioned = mentionedUserIds.includes(user.userId)

          // 根据是否已艾特添加不同的样式和状态
          const itemClass = isAlreadyMentioned ? 'mention-item mentioned-disabled' : 'mention-item'
          const checkIcon = isAlreadyMentioned ? '<span class="mention-check">✓</span>' : ''

          return `
            <div class="${itemClass}" data-mentioned="${isAlreadyMentioned}">
              <img class="mention-avatar" src="${avatarSrc}" alt="${user.userName}" onerror="if(this.src!=='${defaultAvatar}')this.src='${defaultAvatar}'">
              <div class="mention-info">
                <div class="mention-name">${user.nickName || user.userName}</div>
                <div class="mention-username">@${user.userName}</div>
              </div>
              ${checkIcon}
            </div>
          `
        }
      })

      this.tribute.attach(editableElement)

      // 添加事件监听来阻止选择已艾特的用户
      editableElement.addEventListener('tribute-active-true', () => {
        // 当艾特菜单显示时，添加点击事件监听
        setTimeout(() => {
          const tributeContainer = document.querySelector('.tribute-container')
          if (tributeContainer) {
            tributeContainer.addEventListener('click', this.handleMentionClick, true)
          }
        }, 0)
      })

      editableElement.addEventListener('tribute-active-false', () => {
        // 当艾特菜单隐藏时，移除点击事件监听
        const tributeContainer = document.querySelector('.tribute-container')
        if (tributeContainer) {
          tributeContainer.removeEventListener('click', this.handleMentionClick, true)
        }
      })
    },

    // 获取可编辑元素
    getEditableElement() {
      return this.$refs.editableDiv
    },

    // 从服务器搜索用户（统一的用户搜索方法）
    async fetchUsersFromServer(keyword = '') {
      try {
        this.isLoading = true

        if (!this.drillTaskId) {
          this.users = []
          return []
        }

        const response = await getUsersForMention({
          drillTaskId: this.drillTaskId,
          keyword: keyword, // 搜索关键词，为空时获取所有用户
          pageNum: 1,
          pageSize: 50
        })

        let users = []
        if (response && response.data) {
          users = response.data
        }

        // 如果是初始加载（无关键词），更新用户列表
        if (!keyword) {
          this.users = users
          console.log('初始加载用户数据:', users.length, '个用户')
        } else {
          console.log(`搜索关键词 "${keyword}" 结果:`, users.length, '个用户')
        }

        return users
      } catch (error) {
        console.error('获取用户列表失败:', error)
        if (!keyword) {
          this.$message.error('加载用户列表失败')
        }
        return []
      } finally {
        this.isLoading = false
      }
    },

    // 加载用户列表（初始化时调用）
    async loadUsers() {
      await this.fetchUsersFromServer()
    },

    // 搜索用户（使用后端搜索）
    searchUsers(text, callback) {
      const searchText = text.trim()

      // 如果没有关键词，显示初始用户列表
      if (!searchText) {
        const formattedUsers = this.formatUsersForTribute(this.users, '')
        callback(formattedUsers)
        return
      }

      // 如果关键词与上次相同，避免重复请求
      if (searchText === this.lastSearchKeyword) {
        return
      }

      // 清除之前的防抖定时器
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer)
      }

      // 设置防抖定时器，300ms 后执行搜索
      this.searchDebounceTimer = setTimeout(async () => {
        try {
          this.lastSearchKeyword = searchText
          const users = await this.fetchUsersFromServer(searchText)
          const formattedUsers = this.formatUsersForTribute(users, searchText)
          callback(formattedUsers)
        } catch (error) {
          console.error('搜索用户失败:', error)
          callback([])
        }
      }, 300)
    },

    // 格式化用户数据供 Tribute 使用
    formatUsersForTribute(users, searchText = '') {
      return users
        .slice(0, this.maxUsers)
        .map(user => ({
          key: user.userName,
          value: user.nickName || user.userName,
          // 添加一个包含搜索文本的字段，确保 Tribute 认为这是匹配的
          searchableText: searchText + (user.nickName || user.userName) + user.userName,
          userName: user.userName,
          nickName: user.nickName,
          avatar: user.avatar ? `${process.env.VUE_APP_BASE_API}${user.avatar}` : null,
          userId: user.userId
        }))
    },

    // 处理输入事件
    handleInput(event) {
      // 延迟处理，确保 DOM 更新完成
      this.$nextTick(() => {
        this.updateValueFromContent()
      })
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 如果有长度限制且已达到限制，阻止继续输入（除了删除键、方向键等）
      if (this.isLimitLength && this.textLength >= this.upperLimit) {
        const allowedKeys = [
          'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
          'Home', 'End', 'Tab', 'Escape', 'Enter'
        ]

        // 允许 Ctrl/Cmd 组合键（如复制、粘贴、全选等）
        if (!allowedKeys.includes(event.key) && !event.ctrlKey && !event.metaKey) {
          event.preventDefault()
          return
        }
      }
    },

    // 处理粘贴事件
    handlePaste(event) {
      if (!this.isLimitLength) return

      event.preventDefault()

      // 获取粘贴的文本
      const pasteText = (event.clipboardData || window.clipboardData).getData('text')
      if (!pasteText) return

      // 计算可以插入的文本长度
      const remainingLength = this.upperLimit - this.textLength
      if (remainingLength <= 0) return

      // 截取可插入的文本
      const insertText = pasteText.substring(0, remainingLength)

      // 插入文本
      document.execCommand('insertText', false, insertText)
    },

    // 处理焦点事件
    handleFocus(event) {
      this.$emit('focus', event)
    },

    // 处理失焦事件
    handleBlur(event) {
      this.$emit('blur', event)
    },

    // 设置可编辑区域内容
    setEditableContent() {
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return

      // 处理空内容或只包含空白字符的情况
      if (!this.localValue || !this.localValue.trim()) {
        editableDiv.innerHTML = ''
        return
      }

      // 将文本中的 @用户名 转换为带样式的标签
      let html = this.localValue
      const mentionRegex = new RegExp(`${this.trigger}([\\w\\u4e00-\\u9fa5]+)`, 'g')
      html = html.replace(mentionRegex, (match, userName) => {
        const user = this.users.find(u => u.userName === userName)
        if (user) {
          return this.createMentionTag(user)
        }
        return match
      })

      editableDiv.innerHTML = html
    },

    // 从可编辑区域更新值
    updateValueFromContent() {
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return

      // 提取纯文本，将艾特标签转换为 @用户名 格式
      let text = editableDiv.innerText || editableDiv.textContent || ''

      // 处理空内容的情况：去除只包含换行符、空格等的内容
      text = text.replace(/^\s+$/g, '').replace(/^\n+$/g, '')

      // 如果内容为空，清空 editableDiv
      if (!text.trim()) {
        text = ''
        if (editableDiv.innerHTML !== '') {
          editableDiv.innerHTML = ''
        }
      }

      // 如果有长度限制，截取文本
      if (this.isLimitLength && text.length > this.upperLimit) {
        text = text.substring(0, this.upperLimit)

        // 更新可编辑区域内容
        this.$nextTick(() => {
          this.setEditableContentWithText(text)
        })
      }

      this.localValue = text
      this.$emit('input', text)
    },
    // 使用纯文本设置可编辑区域内容
    setEditableContentWithText(text) {
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return

      // 将文本中的 @用户名 转换为带样式的标签
      let html = text
      const mentionRegex = new RegExp(`${this.trigger}([\\w\\u4e00-\\u9fa5]+)`, 'g')
      html = html.replace(mentionRegex, (match, userName) => {
        const user = this.users.find(u => u.userName === userName)
        if (user) {
          return this.createMentionTag(user)
        }
        return match
      })

      editableDiv.innerHTML = html

      // 将光标移到末尾
      this.setCursorToEnd()
    },

    // 设置光标到末尾
    setCursorToEnd() {
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return

      const range = document.createRange()
      const selection = window.getSelection()

      range.selectNodeContents(editableDiv)
      range.collapse(false)
      selection.removeAllRanges()
      selection.addRange(range)
    },



    // 获取提交数据格式
    getSubmitData() {
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return { content: '', mentionUsers: [] }

      const content = editableDiv.innerText || editableDiv.textContent || ''
      const mentionUsers = this.getMentionedUsersWithPosition()

      return {
        content: content,
        mentionUsers: mentionUsers
      }
    },

    // 获取带精确位置信息的艾特用户列表
    getMentionedUsersWithPosition() {
      const mentions = []
      const editableDiv = this.getEditableElement()
      if (!editableDiv) return mentions

      // 获取纯文本内容
      const textContent = editableDiv.innerText || editableDiv.textContent || ''

      // 获取所有艾特标签
      const mentionSpans = editableDiv.querySelectorAll('.mention-tag')

      // 记录已经处理过的位置，用于处理重复艾特
      const processedPositions = []

      mentionSpans.forEach(span => {
        const userId = span.getAttribute('data-user-id')
        const userName = span.getAttribute('data-user-name')
        const mentionText = span.textContent || span.innerText || ''
        const user = this.users.find(u => u.userId == userId)

        if (user && mentionText) {
          // 查找这个艾特文本在纯文本中的所有位置
          const allPositions = this.findAllMentionPositions(textContent, mentionText)

          // 为每个位置创建一个艾特记录
          allPositions.forEach(startPos => {
            // 检查这个位置是否已经被处理过
            if (!processedPositions.includes(startPos)) {
              processedPositions.push(startPos)

              mentions.push({
                mentionText: mentionText.replace('@', ''), // 去掉@符号，只保留用户名
                startPos: startPos, // @符号开始的位置
                endPos: startPos + mentionText.length, // 整个@用户名的结束位置
                userId: user.userId,
                userName: user.userName,
                nickName: user.nickName
              })
            }
          })
        }
      })

      // 使用公共方法进行排序（保留所有艾特，包括重复的）
      return this.sortMentions(mentions)
    },

    // 查找文本中所有匹配的位置
    findAllMentionPositions(textContent, mentionText) {
      const positions = []
      let searchStart = 0

      while (true) {
        // 从当前搜索位置开始查找
        let pos = textContent.indexOf(mentionText, searchStart)

        if (pos === -1) {
          // 如果没找到，尝试重新构建艾特文本进行查找
          const userDisplayName = mentionText.replace('@', '')
          const atUserName = '@' + userDisplayName
          pos = textContent.indexOf(atUserName, searchStart)
        }

        if (pos === -1) {
          break // 没有更多匹配项
        }

        positions.push(pos)
        searchStart = pos + 1 // 从下一个字符开始继续搜索
      }

      return positions
    },


  }
}
</script>

<style lang="scss" scoped>
.mention-input-wrapper {
  position: relative;
  width: 100%;

  .mention-editable {
    min-height: 32px;
    max-height: 60vh;
    overflow-y: auto;
    padding: 5px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    line-height: 1.5;
    color: #606266;
    background-color: #fff;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    outline: none;
    box-sizing: border-box;
    word-wrap: break-word;
    word-break: break-all;

    &:focus {
      border-color: #409eff;
    }

    &.is-exceed {
      border-color: #f56c6c;
    }

    &:empty:before {
      content: attr(placeholder);
      color: #c0c4cc;
      pointer-events: none;
    }


    /* 艾特标签样式 */
    ::v-deep .mention-tag {
      display: inline-block;
      // padding: 2px 6px;
      // margin: 0 2px;
      // background-color: #409eff;
      // color: #fff;
      color: #409eff;
      // border-radius: 3px;
      // font-size: 12px;
      // line-height: 1.2;
      cursor: pointer;
      user-select: none;
      white-space: nowrap;
      vertical-align: middle;

      /* 防止在艾特标签内部出现光标 */
      * {
        pointer-events: none;
      }

      &:hover {
        // background-color: #66b1ff;
        color: #66b1ff;
      }
    }
  }

  // 字数统计样式
  .mention-count {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 12px;
    color: #909399;
    line-height: 14px;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 5px;

    .is-exceed {
      color: #f56c6c;
    }
  }
}

.mention-editable.is-textarea {
  min-height: 80px;
  padding: 5px 15px;
  resize: vertical;
}
</style>

<style>
/* Tribute 样式 - 不使用 scoped */
.tribute-container {
  position: absolute;
  top: 0;
  left: 0;
  height: auto;
  max-height: 300px;
  max-width: 320px;
  min-width: 240px;
  overflow: auto;
  display: block;
  z-index: 999999;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

.tribute-container ul {
  margin: 0;
  margin-top: 2px;
  padding: 0;
  list-style: none;
  background: #fff;
}

.tribute-container li {
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.2s ease;
}

.tribute-container li:last-child {
  border-bottom: none;
}

.tribute-container li.highlight,
.tribute-container li:hover {
  background: #ecf5ff;
}

.mention-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mention-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.mention-info {
  flex: 1;
  min-width: 0;
}

.mention-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mention-username {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 已艾特用户的样式 */
.mention-item.mentioned-disabled {
  opacity: 0.6;
  position: relative;
}

.tribute-container li:has(.mentioned-disabled) {
  cursor: not-allowed !important;
  background: #f5f7fa !important;
}

.tribute-container li:has(.mentioned-disabled):hover,
.tribute-container li:has(.mentioned-disabled).highlight {
  background: #f5f7fa !important;
}

.mention-item.mentioned-disabled .mention-name {
  color: #c0c4cc;
}

.mention-item.mentioned-disabled .mention-username {
  color: #c0c4cc;
}

.mention-item.mentioned-disabled .mention-avatar {
  filter: grayscale(50%);
}

.mention-check {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
  margin-left: auto;
  flex-shrink: 0;
}

.mention-no-match {
  padding: 8px 12px;
  color: #909399;
  font-size: 14px;
  text-align: center;
}
</style>
