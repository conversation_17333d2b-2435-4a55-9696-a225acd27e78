<?xml version="1.0" encoding="UTF-8"?>
<svg width="351px" height="487px" preserveAspectRatio="none meet" viewBox="0 0 351 487" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 6</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="任务演练-红方队长-发布评论-情况通报" transform="translate(-1454, -759)">
            <g id="编组-6" transform="translate(1454, 759)">
                <rect id="矩形备份-31" fill="#0A3E8D" x="0" y="0" width="350.183946" height="487"></rect>
                <text id="情况通报备份" fill-rule="nonzero" font-family="STSongti-SC-Regular, Songti SC" font-size="30" font-weight="normal" line-spacing="30" fill="#FFFFFF">
                    <tspan x="115" y="88">情况通报</tspan>
                </text>
                <text id="xxxxxxxxxxxxxxxxxxxx" fill-rule="nonzero" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal" line-spacing="30" fill="#FFFFFF">
                    <tspan x="50" y="146">     xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</tspan>
                    <tspan x="50" y="176">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</tspan>
                    <tspan x="50" y="206">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</tspan>
                    <tspan x="50" y="236">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</tspan>
                    <tspan x="50" y="266">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</tspan>
                    <tspan x="50" y="296">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx。</tspan>
                </text>
                <text id="xxxxxx-20xx年xx日x-x日" fill-rule="nonzero" font-family="PingFangSC-Regular, PingFang SC" font-size="10" font-weight="normal" line-spacing="20" fill="#FFFFFF">
                    <tspan x="270.46" y="369">xxxxxx</tspan>
                    <tspan x="225.13" y="389">20xx年xx日x x日</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>