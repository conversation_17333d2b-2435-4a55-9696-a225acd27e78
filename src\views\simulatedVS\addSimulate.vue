<template>
    <div class="addSimulate">
        <div class="pageTitle">
            <div class="pageTitle_colorBlock" />
            <div class="pageTitle_title">{{ pageTitle }}</div>
        </div>
        <div class="dashedLine"></div>
        <div class="main-content">
            <el-form ref="form" :model="taskForm" label-width="160px" :rules="rules">

                <el-form-item label="任务标题：" prop="taskTitle">
                    <el-input v-model.trim="taskForm.taskTitle" maxlength="100" show-word-limit placeholder="请输入标题"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item label="任务内容：" prop="taskContent">
                    <el-input type="textarea" v-model.trim="taskForm.taskContent" maxlength="1000" show-word-limit
                        :autosize="{ minRows: 4, maxRows: 6}" placeholder="请输入任务内容，不超过1000个字符" style="width: 100%;" />
                </el-form-item>
                <el-form-item label="演练事件：" prop="drillEvent">
                    <el-input v-model.trim="taskForm.drillEvent" maxlength="100" show-word-limit placeholder="请输入事件"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item label="演练时间：" prop="estimateDrillTime">
                    <el-date-picker v-model="taskForm.estimateDrillTime" type="date" :picker-options="pickOptions"
                        value-format="yyyy-MM-dd" placeholder="请选择演练时间" style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="演练阶段：" prop="drillProcessStages">
                    <div class="drill-stages-container">
                        <div class="stages-list">
                            <div
                                v-for="(stage, index) in taskForm.drillProcessStages"
                                :key="index"
                                class="stage-item"
                                :class="{ 'stage-disabled': stage.disabled }"
                            >
                                <div class="stage-content">
                                    <el-input
                                        v-model="stage.stageName"
                                        placeholder="请输入阶段名称"
                                        :disabled="stage.disabled"
                                        @blur="updateStageOrder"
                                        maxlength="15"
                                    />
                                    <div class="stage-actions">
                                        <el-button
                                            v-if="stage.disabled"
                                            type="text"
                                            icon="el-icon-plus"
                                            @click="enableStage(index)"
                                            class="add-btn"
                                            title="激活阶段"
                                        />
                                        <el-button
                                            v-if="!stage.disabled"
                                            type="text"
                                            icon="el-icon-minus"
                                            @click="disableStage(index)"
                                            class="remove-btn"
                                            title="禁用阶段"
                                        />
                                    </div>
                                </div>
                                <div v-if="index < taskForm.drillProcessStages.length - 1" class="stage-arrow">
                                    <i class="el-icon-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <div class="group-top">
                    <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                    <div>蓝方</div>
                </div>
                <!-- 蓝方队长 -->
                <el-form-item label="队长：">
                    <el-select v-model="taskForm.blueCaptain" placeholder="请选择队长（非必选）" style="width: 100%" filterable clearable>
                        <el-option v-for="item in blueCaptainOptions" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- 蓝方小组 -->
                <el-form-item label="小组：">
                    <div class="groups-container">
                        <div v-for="(group, index) in taskForm.blueGroups" :key="index" class="group-item">
                            <div class="group-form">
                                <div class="group-row">
                                    <label>组名：<span style="color: red;">*</span></label>
                                    <el-input v-model="group.groupName" maxlength="10" show-word-limit placeholder="请输入组名" style="width: 100%;" clearable/>
                                </div>
                                <div class="group-row">
                                    <label>组长：</label>
                                    <el-select v-model="group.captain" placeholder="请选择组长（非必选）" style="width: 100%;" filterable clearable>
                                        <el-option v-for="item in getBlueGroupCaptainOptions(index)" :key="item.value"
                                            :label="item.label" :value="item.value" :disabled="item.disabled">
                                        </el-option>
                                    </el-select>
                                </div>
                                <div class="group-row">
                                    <label>组员：</label>
                                    <el-select v-model="group.members" multiple placeholder="请选择组员（非必选）" style="width: 100%;" filterable clearable>
                                        <el-option v-for="item in getBlueGroupMemberOptions(index)" :key="item.value"
                                            :label="item.label" :value="item.value" :disabled="item.disabled">
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="group-actions">
                                <el-button type="primary" icon="el-icon-plus" circle size="mini"
                                    @click="addBlueGroup(index)" title="新增小组"></el-button>
                                <el-button type="danger" icon="el-icon-minus"
                                    circle size="mini" @click="removeBlueGroup(index)" title="删除小组"></el-button>
                            </div>
                        </div>
                        <div v-if="taskForm.blueGroups.length === 0" class="no-groups">
                            <el-button type="primary" icon="el-icon-plus" @click="addBlueGroup(-1)">添加蓝方小组</el-button>
                        </div>
                    </div>
                </el-form-item>
                <!-- 人员数量 -->
                <el-form-item label="最多人员数量：" prop="blueMemberLimit">
                    <el-input v-model="taskForm.blueMemberLimit" style="width: 100px;" controls-position="right"
                        @input="blueMemberLimitInput"></el-input> 人
                </el-form-item>


                <div class="group-top">
                    <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                    <div>红方</div>
                </div>
                <!-- 红方队长 -->
                <el-form-item label="队长：">
                    <el-select v-model="taskForm.redCaptain" placeholder="请选择队长（非必选）" style="width: 100%" filterable clearable>
                        <el-option v-for="item in redCaptainOptions" :key="item.value" :label="item.label"
                            :value="item.value" :disabled="item.disabled">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- 红方小组 -->
                <el-form-item label="小组：">
                    <div class="groups-container">
                        <div v-for="(group, index) in taskForm.redGroups" :key="index" class="group-item">
                            <div class="group-form">
                                <div class="group-row">
                                    <label>组名：<span style="color: red;">*</span></label>
                                    <el-input v-model="group.groupName" maxlength="10" show-word-limit placeholder="请输入组名" style="width: 100%;" clearable/>
                                </div>
                                <div class="group-row">
                                    <label>组长：</label>
                                    <el-select v-model="group.captain" placeholder="请选择组长（非必选）" style="width: 100%;" filterable clearable>
                                        <el-option v-for="item in getRedGroupCaptainOptions(index)" :key="item.value"
                                            :label="item.label" :value="item.value" :disabled="item.disabled">
                                        </el-option>
                                    </el-select>
                                </div>
                                <div class="group-row">
                                    <label>组员：</label>
                                    <el-select v-model="group.members" multiple placeholder="请选择组员（非必选）" style="width: 100%;" filterable clearable>
                                        <el-option v-for="item in getRedGroupMemberOptions(index)" :key="item.value"
                                            :label="item.label" :value="item.value" :disabled="item.disabled">
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="group-actions">
                                <el-button type="primary" icon="el-icon-plus" circle size="mini"
                                    @click="addRedGroup(index)" title="新增小组"></el-button>
                                <el-button type="danger" icon="el-icon-minus"
                                    circle size="mini" @click="removeRedGroup(index)" title="删除小组"></el-button>
                            </div>
                        </div>
                        <div v-if="taskForm.redGroups.length === 0" class="no-groups">
                            <el-button type="primary" icon="el-icon-plus" @click="addRedGroup(-1)">添加红方小组</el-button>
                        </div>
                    </div>
                </el-form-item>
                <!-- 人员数量 -->
                <el-form-item label="最多人员数量：" prop="redMemberLimit">
                    <el-input v-model="taskForm.redMemberLimit" style="width: 100px;" controls-position="right"
                        @input="redMemberLimitInput"></el-input> 人
                </el-form-item>
            </el-form>
            <div class="btn-group">
                <el-button v-hasRole="['moderator']" v-if="pageTitle=='新增任务演练'||taskForm.status==1" type="primary"
                    @click="onSubmit">确定</el-button>
                <el-button @click="onCancel">取消</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { drillTaskSaveApi, drillTaskUserApi, drillTaskQueryOneApi, drillDefaultStageApi } from "@/api/simulatedVS/index.js";
export default {
    data() {
        return {
            taskForm: {
                taskTitle: '',
                taskContent: '',
                drillEvent: '',
                estimateDrillTime: '',
                blueCaptain: '',
                blueGroups: [],
                redCaptain: '',
                redGroups: [],
                drillProcessStages: [],
            },
            rules: {
                taskTitle: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
                taskContent: [
                    { required: true, message: '请输入任务内容', trigger: 'blur' },
                    { max: 1000, message: "任务内容请控制在1000个字以内", trigger: "change" }
                ],
                drillEvent: [{ required: true, message: '请输入演练事件', trigger: 'blur' }],
                estimateDrillTime: [{ required: true, message: '请输入演练时间', trigger: 'blur' }],
                drillProcessStages: [{ required: true, message: '请至少添加一个演练阶段', trigger: 'blur' }],

                blueMemberLimit: [
                    { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'blur' }
                ],
                redMemberLimit: [
                    { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'blur' }
                ],
            },
            pickOptions: {
                disabledDate: (time) => {
                    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
                }
            },

            peopleList: [],
            pageTitle: '新增任务演练'
        }
    },
    computed: {
        // 获取所有已选择的人员ID
        getAllSelectedUserIds() {
            const selectedIds = []

            // 添加队长
            if (this.taskForm.blueCaptain) selectedIds.push(this.taskForm.blueCaptain)
            if (this.taskForm.redCaptain) selectedIds.push(this.taskForm.redCaptain)

            // 添加蓝方小组的组长和组员
            this.taskForm.blueGroups.forEach(group => {
                if (group.captain) selectedIds.push(group.captain)
                if (group.members) selectedIds.push(...group.members)
            })

            // 添加红方小组的组长和组员
            this.taskForm.redGroups.forEach(group => {
                if (group.captain) selectedIds.push(group.captain)
                if (group.members) selectedIds.push(...group.members)
            })

            return selectedIds
        },

        // 计算蓝队实际人数
        blueTeamMemberCount() {
            let count = 0

            // 队长
            if (this.taskForm.blueCaptain) count++

            // 小组组长和组员
            this.taskForm.blueGroups.forEach(group => {
                if (group.captain) count++
                if (group.members && group.members.length > 0) {
                    count += group.members.length
                }
            })

            return count
        },

        // 计算红队实际人数
        redTeamMemberCount() {
            let count = 0

            // 队长
            if (this.taskForm.redCaptain) count++

            // 小组组长和组员
            this.taskForm.redGroups.forEach(group => {
                if (group.captain) count++
                if (group.members && group.members.length > 0) {
                    count += group.members.length
                }
            })

            return count
        },

        // 蓝队长选项（排除所有已选择的人员）
        blueCaptainOptions() {
            const excludedIds = this.getAllSelectedUserIds.filter(id => id !== this.taskForm.blueCaptain)
            return this.peopleList.map(user => ({
                ...user,
                disabled: excludedIds.includes(user.value)
            }))
        },

        // 红队长选项（排除所有已选择的人员）
        redCaptainOptions() {
            const excludedIds = this.getAllSelectedUserIds.filter(id => id !== this.taskForm.redCaptain)
            return this.peopleList.map(user => ({
                ...user,
                disabled: excludedIds.includes(user.value)
            }))
        }
    },
    mounted() {
        this.getDist()
        this.loadDefaultStages()
        if (this.$route.query?.drillTaskId) {
            this.pageTitle = '编辑任务演练'
            this.resetForm()
            let query = {
                drillTaskId: this.$route.query.drillTaskId
            }
            drillTaskQueryOneApi(query).then(res => {
                if (res.code == '200') {
                    this.taskForm = {
                        ...res.data,
                        blueGroups: (res.data.blueGroups || []).map(group => ({
                            drillGroupId: group.drillGroupId || null,
                            groupName: group.groupName || '',
                            captain: group.captain || '',
                            members: group.members || []
                        })),
                        redGroups: (res.data.redGroups || []).map(group => ({
                            drillGroupId: group.drillGroupId || null,
                            groupName: group.groupName || '',
                            captain: group.captain || '',
                            members: group.members || []
                        }))
                    }
                    // 编辑时始终从演练默认阶段查询API获取阶段数据
                    this.loadDefaultStages()
                }
            })
        } else {
            this.pageTitle = '新增任务演练'
            this.resetForm()
        }
    },
    methods: {
        getDist() {
            drillTaskUserApi().then(res => {
                if (res.code == '200') {
                    this.peopleList = res.data.map(item => {
                        return {
                            value: item.userId,
                            label: item.nickName
                        }
                    })
                }
            })
        },
        // 加载默认演练阶段
        loadDefaultStages() {
          let query = {
            drillTaskId: this.$route.query.drillTaskId
          }
            drillDefaultStageApi(query).then(res => {
                if (res.code == '200' && res.data && res.data.length > 0) {
                    this.taskForm.drillProcessStages = res.data.map((stage, index) => ({
                        processStageId: stage.processStageId,
                        drillTaskId: stage.drillTaskId,
                        stageName: stage.stageName || `阶段${index + 1}`,
                        stageOrder: stage.stageOrder || index + 1,
                        stageType: stage.stageType || 1,
                        stageStatus: 1,
                        timerType: stage.timerType || 2,
                        timerDuration: stage.timerDuration || 300,
                        scoreType: stage.scoreType || 1,
                        stageId: stage.stageId || null,
                        stageShow: stage.stageShow !== undefined ? stage.stageShow : true,
                        disabled: !stage.stageShow // 根据stageShow设置disabled状态
                    }))
                } else {
                    // 如果没有默认阶段，创建一个默认阶段
                    this.taskForm.drillProcessStages = [{
                        processStageId: null,
                        drillTaskId: null,
                        stageName: '阶段1',
                        stageOrder: 1,
                        stageType: 1,
                        stageStatus: 1,
                        timerType: 2,
                        timerDuration: 300,
                        scoreType: 1,
                        stageId: null,
                        stageShow: true,
                        disabled: false
                    }]
                }
            }).catch(() => {
                // 如果API调用失败，创建一个默认阶段
                this.taskForm.drillProcessStages = [{
                    processStageId: null,
                    drillTaskId: null,
                    stageName: '阶段1',
                    stageOrder: 1,
                    stageType: 1,
                    stageStatus: 1,
                    timerType: 2,
                    timerDuration: 300,
                    scoreType: 1,
                    stageId: null,
                    stageShow: true,
                    disabled: false
                }]
            })
        },
        // 激活阶段
        enableStage(index) {
            this.taskForm.drillProcessStages[index].disabled = false
            this.taskForm.drillProcessStages[index].stageShow = true
        },
        // 禁用阶段
        disableStage(index) {
            this.taskForm.drillProcessStages[index].disabled = true
            this.taskForm.drillProcessStages[index].stageShow = false
        },
        // 更新阶段排序
        updateStageOrder() {
            this.taskForm.drillProcessStages.forEach((stage, index) => {
                stage.stageOrder = index + 1
            })
        },
        // 获取蓝方小组组长选项
        getBlueGroupCaptainOptions(groupIndex) {
            const currentGroup = this.taskForm.blueGroups[groupIndex]
            const excludedIds = this.getAllSelectedUserIds.filter(id => id !== currentGroup.captain)
            return this.peopleList.map(user => ({
                ...user,
                disabled: excludedIds.includes(user.value)
            }))
        },

        // 获取蓝方小组组员选项
        getBlueGroupMemberOptions(groupIndex) {
            const currentGroup = this.taskForm.blueGroups[groupIndex]
            const excludedIds = this.getAllSelectedUserIds.filter(id => !currentGroup.members.includes(id))
            return this.peopleList.map(user => ({
                ...user,
                disabled: excludedIds.includes(user.value)
            }))
        },

        // 获取红方小组组长选项
        getRedGroupCaptainOptions(groupIndex) {
            const currentGroup = this.taskForm.redGroups[groupIndex]
            const excludedIds = this.getAllSelectedUserIds.filter(id => id !== currentGroup.captain)
            return this.peopleList.map(user => ({
                ...user,
                disabled: excludedIds.includes(user.value)
            }))
        },

        // 获取红方小组组员选项
        getRedGroupMemberOptions(groupIndex) {
            const currentGroup = this.taskForm.redGroups[groupIndex]
            const excludedIds = this.getAllSelectedUserIds.filter(id => !currentGroup.members.includes(id))
            return this.peopleList.map(user => ({
                ...user,
                disabled: excludedIds.includes(user.value)
            }))
        },

        // 添加蓝方小组
        addBlueGroup(index) {
            const newGroupIndex = this.taskForm.blueGroups.length + 1
            const newGroup = {
                drillGroupId: null, // 新增小组没有drillGroupId
                groupName: `蓝队${newGroupIndex}组`,
                captain: '',
                members: []
            }
            if (index === -1) {
                // 添加第一个小组
                this.taskForm.blueGroups.push(newGroup)
            } else {
                // 在指定位置后添加小组
                this.taskForm.blueGroups.splice(index + 1, 0, newGroup)
            }
        },

        // 删除蓝方小组
        removeBlueGroup(index) {
            this.taskForm.blueGroups.splice(index, 1)
            // 重新编号剩余小组
            this.taskForm.blueGroups.forEach((group, idx) => {
                if (group.groupName.startsWith('蓝队') && group.groupName.endsWith('组')) {
                    group.groupName = `蓝队${idx + 1}组`
                }
            })
        },

        // 添加红方小组
        addRedGroup(index) {
            const newGroupIndex = this.taskForm.redGroups.length + 1
            const newGroup = {
                drillGroupId: null, // 新增小组没有drillGroupId
                groupName: `红队${newGroupIndex}组`,
                captain: '',
                members: []
            }
            if (index === -1) {
                // 添加第一个小组
                this.taskForm.redGroups.push(newGroup)
            } else {
                // 在指定位置后添加小组
                this.taskForm.redGroups.splice(index + 1, 0, newGroup)
            }
        },

        // 删除红方小组
        removeRedGroup(index) {
            this.taskForm.redGroups.splice(index, 1)
            // 重新编号剩余小组
            this.taskForm.redGroups.forEach((group, idx) => {
                if (group.groupName.startsWith('红队') && group.groupName.endsWith('组')) {
                    group.groupName = `红队${idx + 1}组`
                }
            })
        },

        validateTeams() {
            const errors = []

            // 验证蓝方小组 - 如果有小组，组名必填
            this.taskForm.blueGroups.forEach((group, index) => {
                if (!group.groupName || group.groupName.trim() === '') {
                    errors.push(`蓝队第${index + 1}组的组名不能为空`)
                }
            })

            // 验证红方小组 - 如果有小组，组名必填
            this.taskForm.redGroups.forEach((group, index) => {
                if (!group.groupName || group.groupName.trim() === '') {
                    errors.push(`红队第${index + 1}组的组名不能为空`)
                }
            })

            return errors
        },
        onSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {

                    const errors = this.validateTeams()
                    if (errors.length > 0) {
                        this.$message.error(errors.join('，'))
                        return
                    }

                    // 验证演练阶段 - 至少要有一个启用的阶段
                    const enabledStages = this.taskForm.drillProcessStages.filter(stage => !stage.disabled && stage.stageName.trim())
                    if (enabledStages.length === 0) {
                        this.$message.error('请至少启用一个演练阶段')
                        return
                    }

                    // 验证队伍人员数量
                    const memberCountErrors = []

                    // 验证蓝队人员数量
                    if (this.taskForm.blueMemberLimit && this.blueTeamMemberCount > parseInt(this.taskForm.blueMemberLimit)) {
                        memberCountErrors.push(`蓝队人员数量(${this.blueTeamMemberCount}人)超过最多人员限制(${this.taskForm.blueMemberLimit}人)`)
                    }

                    // 验证红队人员数量
                    if (this.taskForm.redMemberLimit && this.redTeamMemberCount > parseInt(this.taskForm.redMemberLimit)) {
                        memberCountErrors.push(`红队人员数量(${this.redTeamMemberCount}人)超过最多人员限制(${this.taskForm.redMemberLimit}人)`)
                    }

                    if (memberCountErrors.length > 0) {
                        this.$message.error(memberCountErrors.join('，'))
                        return
                    }

                    // 传递所有默认阶段，通过stageShow标识启用状态
                    const params = {
                        ...this.taskForm,
                        // 确保小组数据包含drillGroupId
                        blueGroups: this.taskForm.blueGroups.map(group => ({
                            drillGroupId: group.drillGroupId || null, // 保留drillGroupId，新增小组为null
                            groupName: group.groupName,
                            captain: group.captain,
                            members: group.members
                        })),
                        redGroups: this.taskForm.redGroups.map(group => ({
                            drillGroupId: group.drillGroupId || null, // 保留drillGroupId，新增小组为null
                            groupName: group.groupName,
                            captain: group.captain,
                            members: group.members
                        })),
                        drillProcessStages: this.taskForm.drillProcessStages.map((stage, index) => ({
                            ...stage,
                            stageOrder: index + 1,
                            stageShow: !stage.disabled // 根据disabled状态设置stageShow
                        }))
                    }
                    console.log('params', params)
                    drillTaskSaveApi(params).then((res) => {
                        this.$message.success('保存成功')
                        this.onCancel()
                    }).catch((err) => {
                        this.$message.error('保存失败')
                    })
                }
            })
        },
        onCancel() {
            // this.$router.go(-1)
            this.$router.push({ path: '/simulatedVS/index' })
        },
        resetForm() {
            this.$refs.form.resetFields()
            this.taskForm = {
                taskTitle: '',
                taskContent: '',
                drillEvent: '',
                estimateDrillTime: '',
                blueCaptain: '',
                blueGroups: [],
                redCaptain: '',
                redGroups: [],
                drillProcessStages: [],
            }
        },
        // 处理人员数量输入，只允许正整数或空字符串
        redMemberLimitInput(value) {
            // 如果是空字符串，直接允许
            if (value === '') {
                this.taskForm.redMemberLimit = ''
                return
            }

            // 移除所有非数字字符
            const numericValue = value.replace(/[^\d]/g, '')

            // 如果处理后为空，设置为空字符串
            if (numericValue === '') {
                this.taskForm.redMemberLimit = ''
                return
            }

            // 移除前导零（但保留单个0）
            const cleanValue = numericValue.replace(/^0+/, '') || '0'

            // 如果值为0，设置为空字符串（因为我们只要正整数）
            if (cleanValue === '0') {
                this.taskForm.redMemberLimit = ''
                return
            }

            this.taskForm.redMemberLimit = cleanValue
        },
        blueMemberLimitInput(value) {
            // 如果是空字符串，直接允许
            if (value === '') {
                this.taskForm.blueMemberLimit = ''
                return
            }

            // 移除所有非数字字符
            const numericValue = value.replace(/[^\d]/g, '')

            // 如果处理后为空，设置为空字符串
            if (numericValue === '') {
                this.taskForm.blueMemberLimit = ''
                return
            }

            // 移除前导零（但保留单个0）
            const cleanValue = numericValue.replace(/^0+/, '') || '0'

            // 如果值为0，设置为空字符串（因为我们只要正整数）
            if (cleanValue === '0') {
                this.taskForm.blueMemberLimit = ''
                return
            }

            this.taskForm.blueMemberLimit = cleanValue
        },
    }
}
</script>
<style scoped lang="scss">
.pageTitle {
    padding: 12px 0;
    // border-bottom: 1px dashed #DCDEE0;
    background-color: #fff;
    position: relative;

    .pageTitle_colorBlock {
        width: 6px;
        height: 16px;
        background: #247CFF;
        display: inline-block;
        vertical-align: middle;
        margin-right: 20px;
    }

    .pageTitle_title {
        font-size: 18px;
        color: #333333;
        line-height: 25px;
        display: inline-block;
        vertical-align: middle;
        font-weight: 600;
    }

}

.dashedLine {
    width: 100%;
    border-bottom: 1px dashed #DCDEE0;
}

.addSimulate {
    margin: 20px;
    background-color: #fff;

    .main-content {
        padding: 20px 10%;

        .group-top {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            margin-left: 80px;
            margin-bottom: 10px;

            img {
                height: 12px;
                margin-right: 5px;
            }
        }

        .btn-group {
            width: 100%;
            text-align: center;
        }

        // 小组样式
        .groups-container {
            .group-item {
                display: flex;
                align-items: flex-start;
                margin-bottom: 20px;
                padding: 15px;
                border: 1px solid #DCDFE6;
                border-radius: 6px;
                background-color: #FAFAFA;

                .group-form {
                    flex: 1;

                    .group-row {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        label {
                            width: 60px;
                            text-align: right;
                            margin-right: 10px;
                            font-weight: 500;
                            color: #606266;
                        }
                    }
                }

                .group-actions {
                    margin-left: 15px;
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    ::v-deep{
                        button{
                            margin-left: 0;
                        }
                    }
                }
            }

            .no-groups {
                text-align: center;
                padding: 20px;
                border: 2px dashed #DCDFE6;
                border-radius: 6px;
                background-color: #FAFBFC;

                .el-button {
                    font-size: 14px;
                }
            }
        }

        // 演练阶段样式
        .drill-stages-container {
            .stages-list {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                gap: 10px 0;

                .stage-item {
                    display: flex;
                    align-items: center;
                    position: relative;

                    .stage-content {
                        display: flex;
                        align-items: center;
                        border: 1px solid #DCDFE6;
                        border-radius: 4px;
                        padding: 0px 5px;
                        background-color: #fff;
                        position: relative;
                        min-width: 120px;

                        .el-input {
                            border: none;

                            ::v-deep .el-input__inner {
                                border: none;
                                padding: 0;
                                height: auto;
                                line-height: 1.5;
                                background: transparent;
                            }
                        }

                        .stage-actions {
                            position: absolute;
                            top: -8px;
                            right: -8px;
                            display: flex;
                            gap: 2px;

                            .el-button {
                                width: 16px;
                                height: 16px;
                                border-radius: 50%;
                                padding: 0;
                                color: #fff;
                                font-size: 10px;

                                &.add-btn {
                                    background-color: #67C23A;
                                    border-color: #67C23A;

                                    &:hover {
                                        background-color: #85CE61;
                                        border-color: #85CE61;
                                    }
                                }

                                &.remove-btn {
                                    background-color: #F56C6C;
                                    border-color: #F56C6C;

                                    &:hover {
                                        background-color: #F78989;
                                        border-color: #F78989;
                                    }

                                    &:disabled {
                                        background-color: #C0C4CC;
                                        border-color: #C0C4CC;
                                        cursor: not-allowed;
                                    }
                                }
                            }
                        }
                    }

                    .stage-arrow {
                        margin: 0 3px;
                        color: #909399;
                        font-size: 14px;
                    }

                    &.stage-disabled {
                        .stage-content {
                            background-color: #F5F7FA;
                            border-color: #E4E7ED;
                            opacity: 0.6;

                            .el-input {
                                ::v-deep .el-input__inner {
                                    color: #C0C4CC;
                                    background-color: #F5F7FA;
                                }
                            }
                        }

                        .stage-arrow {
                            opacity: 0.5;
                        }
                    }
                }
            }
        }
    }
}
</style>