<template>
  <div class="template1">
    <img class="template1-top" src="@/assets/images/simulatedVS/hotSearchTemp.png" alt="">
    <div class="template1-item" v-show="Number(rank)-1>0">
      <div class="template1-item-rank">{{ Number(rank)-1 }}.</div>
      <div class="template1-item-content" style="filter: blur(0.2em);">
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</div>
      <div class="template1-item-num" style="filter: blur(0.2em);">9999</div>
      <img class="template1-item-icon" src="@/assets/images/simulatedVS/hotIcon.png" alt="">
    </div>
    <div class="template1-item">
      <div class="template1-item-rank">{{ Number(rank)||'' }}.</div>
      <div class="template1-item-content">{{ content||'' }}</div>
      <div class="template1-item-num">{{ hotNum||'' }}</div>
      <img v-if="rankType=='热'" class="template1-item-icon" src="@/assets/images/simulatedVS/hotIcon.png" alt="">
      <img v-else-if="rankType=='新'" class="template1-item-icon" src="@/assets/images/simulatedVS/newIcon.png" alt="">
      <img v-else class="template1-item-icon" alt="">
    </div>
    <div class="template1-item">
      <div class="template1-item-rank">{{ Number(rank)+1 }}.</div>
      <div class="template1-item-content" style="filter: blur(0.2em);">
        XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</div>
      <div class="template1-item-num" style="filter: blur(0.2em);">9999</div>
      <img class="template1-item-icon" src="@/assets/images/simulatedVS/newIcon.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  name: 'HotSearchTemplate',
  props: {
    // 主要数据对象，包含所有热搜相关信息
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 从 item 对象获取排名，提供默认值和空值处理
    rank() {
      return this.item?.rank || '';
    },

    // 从 item 对象获取内容，提供默认值和空值处理
    content() {
      return this.item?.content || '';
    },

    // 从 item 对象获取热度数值，提供默认值和空值处理
    hotNum() {
      return this.item?.hotNum || '';
    },

    // 从 item 对象获取排名类型，提供默认值和空值处理
    rankType() {
      return this.item?.rankType || '';
    }
  }
}
</script>

<style scoped lang="scss">
.template1 {
  font-size: 14em;
  padding: 0 15%;

  .template1-top {
    width: 100%;
    margin-bottom: -0.4em;
  }

  .template1-item {
    background-color: #FFFFFF;
    color: #000000;
    display: flex;
    align-items: center;
    padding: 0 0.5em;

    .template1-item-rank {
      width: 1.5em;
      text-align: right;
      flex-shrink: 0;
      margin-right: 0.5em;
      color: #F9A414;
    }

    .template1-item-content {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      flex: 1;
    }

    .template1-item-num {
      color: #666666;
    }

    .template1-item-icon {
      width: 1em;
      margin-left: 1em;
    }
  }
}
</style>
