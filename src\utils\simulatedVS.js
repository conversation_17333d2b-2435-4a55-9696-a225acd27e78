import { fileGetUrlByIdsApi } from '@/api/simulatedVS'

// 权限常量
export const permissionList = [
  {
    name: "点赞",
    value: "1",
  },
  {
    name: "评论",
    value: "2",
  },
  {
    name: "发帖",
    value: "3",
  },
  {
    name: "热搜",
    value: "4",
  },
  {
    name: "指令",
    value: "5",
  },
];

// 视频文件类型
export const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];

/**
 * 处理单个项目的文件，获取文件URL并排序
 * @param {Object} item - 需要处理文件的对象
 * @param {Array} videoTypes - 视频文件类型数组，默认使用内置类型
 * @returns {Promise<Object>} - 处理后的对象
 */
export async function processItemFiles(item, videoTypesArray = videoTypes) {
  if (!item || !item.file || item.file.length === 0) {
    return item;
  }

  // 如果已经处理过，直接返回
  if (item.fileProcessed) {
    return item;
  }

  try {
    // 获取所有文件的URL
    const res = await fileGetUrlByIdsApi(item.file);

    // 按文件类型排序（视频在后）
    item.fileList = res.data.sort((a, b) => {
      const isVideoA = videoTypesArray.includes(a.type?.split('/').pop());
      const isVideoB = videoTypesArray.includes(b.type?.split('/').pop());
      return isVideoA && !isVideoB ? 1 : !isVideoA && isVideoB ? -1 : 0;
    });

    // 标记已处理防止重复请求
    item.fileProcessed = true;
  } catch (error) {
    console.error('处理文件失败:', error);
    item.fileList = [];
  }

  return item;
}

/**
 * 批量处理评论回复的文件
 * @param {Array} commentReplies - 评论回复数组
 * @param {Array} videoTypes - 视频文件类型数组，默认使用内置类型
 * @returns {Promise<Array>} - 处理后的评论回复数组
 */
export async function processCommentFiles(commentReplies, videoTypesArray = videoTypes) {
  if (!commentReplies || commentReplies.length === 0) {
    return commentReplies;
  }

  return Promise.all(
    commentReplies.map(async (reply) => {
      return await processItemFiles(reply, videoTypesArray);
    })
  );
}

/**
 * 并行处理多个数据项的文件
 * @param {Array} dataArray - 需要处理文件的数据数组
 * @param {Array} videoTypes - 视频文件类型数组，默认使用内置类型
 * @returns {Promise<Array>} - 处理后的数据数组
 */
export async function processBatchFiles(dataArray, videoTypesArray = videoTypes) {
  if (!dataArray || dataArray.length === 0) {
    return dataArray;
  }

  return Promise.all(
    dataArray.map(async (item) => {
      return await processItemFiles(item, videoTypesArray);
    })
  );
}



/**
 * 将文本中的 @mentions 标记为蓝色的 span 标签
 * @param {string} content - 原始文本内容
 * @param {Array} mentions - mentions 数组，包含 mentionText, startPos, endPos 等信息
 * @returns {string} - 处理后的 HTML 字符串
 */
export function markMention(content, mentions = []) {
  if (!content || !mentions || mentions.length === 0) {
    return content;
  }

  // 按照 startPos 从大到小排序，这样从后往前替换不会影响前面的位置
  const sortedMentions = [...mentions].sort((a, b) => b.startPos - a.startPos);

  let result = content;

  // 从后往前处理每个 mention，避免位置偏移
  sortedMentions.forEach(mention => {
    const { mentionText, startPos, endPos } = mention;

    // 验证位置信息的有效性
    if (typeof startPos !== 'number' || typeof endPos !== 'number' ||
        startPos < 0 || endPos <= startPos || endPos > result.length) {
      return; // 跳过无效的 mention
    }

    // 创建蓝色的 span 标签
    const mentionSpan = `<span style="color: #409EFF; font-weight: 500;">@${mentionText}</span>`;

    // 替换文本
    result = result.substring(0, startPos) + mentionSpan + result.substring(endPos);
  });

  return result;
}